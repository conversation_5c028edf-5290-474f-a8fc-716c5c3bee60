import pygame
from typing import Dict, List, Optional, Any, Tuple

from ui.ui_manager import Screen
from utils.logger import logger
from core.game import Game
from utils.resource_manager import resources

class AchievementsScreen(Screen):
    """成就界面"""
    
    def __init__(self, ui_manager, game_manager: Game):
        """
        初始化成就界面
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
        """
        super().__init__("achievements")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        
        # 创建界面组件
        self._create_components()
    
    def _create_components(self):
        """创建界面组件"""
        screen_size = pygame.display.get_surface().get_size()
        
        # 创建标题
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 20, screen_size[0], 40),
            "成就系统",
            "chinese_title",
            align="center"
        )
        self.add_component(title_text)
        
        # 创建返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, screen_size[1] - 60, 120, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_button)
        
        # 创建主面板
        main_panel = self.ui_manager.create_panel(
            pygame.Rect(50, 80, screen_size[0] - 100, screen_size[1] - 160)
        )
        self.add_component(main_panel)
        
        # 添加功能未实现的消息
        info_text = self.ui_manager.create_text(
            pygame.Rect(0, screen_size[1] // 2 - 50, screen_size[0], 100),
            "成就系统正在开发中，请期待后续版本!",
            "chinese_large",
            align="center"
        )
        self.add_component(info_text)
    
    def _on_back_click(self):
        """处理返回按钮点击"""
        self.ui_manager.show_screen("game") 