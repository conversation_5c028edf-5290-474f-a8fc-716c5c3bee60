import random
import json
import time
import os
import traceback
import math
from typing import Dict, List, Any, Tuple, Union, Optional, TYPE_CHECKING

from utils.logger import logger
from utils.resource_manager import resources
from core.config import GameConfig
from core.class_stats import get_class_stats, get_level_stats

# 类型提示导入
if TYPE_CHECKING:
    from core.game import Game
    from core.types import GameType

class Player:
    def __init__(self, character_class="战士", name="", gender="男"):
        """初始化玩家属性"""
        # 检查并设置职业
        if character_class not in GameConfig.CLASS_STATS:
            logger.warning(f"无效的职业: {character_class}，使用默认职业'战士'")
            character_class = "战士"

        self.character_class = character_class
        self.name = name  # 添加玩家名字属性
        self.gender = gender  # 添加玩家性别属性

        # 地图探索相关属性
        self.x = 20.0  # 浮点数以便精确移动
        self.y = 20.0
        self.size = 9
        self.color = (0, 255, 0)  # 绿色
        self.map_speed = 6.0  # 地图移动速度，浮点数

        # 使用class_stats模块获取职业属性
        class_stats = get_class_stats(character_class)

        # 获取基础属性
        base_stats = class_stats["base_stats"]

        # 直接存储属性值
        self._level = 1
        self._exp = 0
        self._gold = 0
        self._yuanbao = 5  # 元宝（稀有货币，初始给5个）

        # VIP系统
        self._vip_level = 0  # VIP等级，默认为0（非VIP）
        self._vip_activated = []  # 已激活的VIP等级列表，避免重复激活

        # 生命值和魔法值
        self._hp = base_stats[0]
        self._max_hp = base_stats[0]
        self._mp = base_stats[1]
        self._max_mp = base_stats[1]

        # 攻击和防御
        self._attack = base_stats[2]
        self._defense = base_stats[3]

        # 缓存属性
        self._cached_magic_attack = None
        self._magic_attack_cache_time = 0

        # 其他属性
        self._attack_speed = base_stats[11]
        self._base_attack_speed = base_stats[11]  # 添加基础攻速属性用于UI显示
        self._crit_rate = 0  # 移除基础暴击率，只使用装备提供的暴击
        self._base_accuracy = class_stats["accuracy"]
        self._agility = class_stats["agility"]
        self._base_luck = 0  # 基础幸运值设为0
        self._luck = 0  # 基础幸运值设为0
        self._magic_defense = class_stats["magic_defense"]
        self._magic_dodge = class_stats["magic_dodge"]

        # 非关键属性
        self.is_dead = False
        self.last_attack_time = time.time()
        self.last_death_time = time.time()
        self.attack_interval = 1.0 / base_stats[11]

        # 背包和装备
        self.inventory = []
        self.max_inventory_slots = 360  # 背包
        self.equipment = {
            "头盔": None,
            "项链": None,
            "左手镯": None,
            "右手镯": None,
            "防具": None,
            "武器": None,
            "勋章": None,
            "左戒指": None,
            "右戒指": None
        }

        # 仓库 - 存储额外物品的空间
        self.storage = []

        # 物品锁定列表，存储已锁定物品的索引
        self.locked_items = set()

        # 属性缓存
        self._equipment_bonus = {}
        self._last_equipment_state = None

        # 游戏实例引用，使用类型提示
        self.game: Optional['Game'] = None

        # 添加初始装备
        self.add_initial_equipment()

        # 记录上次自动恢复的时间
        self.last_regen_time = time.time()

        # 商店刷新符
        self._refresh_tokens = 0  # 商店刷新符数量

        # 技能系统
        self.skills = {}  # 已学习的技能 {skill_id: level}
        self.skill_cooldowns = {}  # 技能冷却状态 {skill_id: cooldown_end_time}
        self.active_skill_effects = []  # 当前激活的技能效果
        self.disabled_skills = set()  # 被禁用的技能ID集合，这些技能不会生效
        self.skill_slots: Dict[int, Optional[str]] = {} # 技能槽位映射 {slot_index: skill_id or None}

        # 技能熟练度系统
        self.skill_proficiencies = {}  # 技能熟练度 {skill_id: proficiency_value}

        # 召唤物和魅惑怪物
        self.summons = []  # 召唤物列表
        self.charmed_monsters = []  # 被魅惑的怪物列表
        self.max_summons = 3  # 最大召唤物数量限制

        # 技能施放状态 (用于暂停普攻)
        self.is_casting_skill = False

        # 全局技能冷却系统
        self.global_skill_cooldown = 0  # 全局技能冷却结束时间
        self.global_skill_cooldown_duration = 0  # 全局技能冷却持续时间

    def add_initial_equipment(self):
        """添加初始装备"""
        try:
            # 从装备数据库中获取初始装备
            equipment_db = GameConfig.loaded_equipment.get("equipment_db", {})
            if not equipment_db:
                logger.warning("装备数据库为空，使用默认配置")
                return

            # 获取新手装备
            newbie_equipment = equipment_db.get("新手装备", [])
            if not newbie_equipment:
                logger.warning("未找到新手装备配置，使用默认配置")
                return

            # 添加初始武器
            weapon = next((item for item in newbie_equipment if item["type"] == "武器"), None)
            if weapon:
                self.equipment["武器"] = weapon.copy()
                logger.info(f"添加初始武器: {weapon['name']}")

            # 添加初始防具(根据性别)
            armor_gender_suffix = f"({self.gender})" if self.gender in ["男", "女"] else "(男)"
            armor = next((item for item in newbie_equipment if item["type"] == "防具" and item["name"].endswith(armor_gender_suffix)), None)

            # 如果找不到对应性别的防具，使用通用防具或默认防具
            if not armor:
                armor = next((item for item in newbie_equipment if item["type"] == "防具" and not any(suffix in item["name"] for suffix in ["(男)", "(女)"])), None)
                if not armor:
                    armor = next((item for item in newbie_equipment if item["type"] == "防具"), None)

            if armor:
                self.equipment["防具"] = armor.copy()
                logger.info(f"添加初始防具: {armor['name']}")

        except Exception as e:
            logger.error(f"添加初始装备失败: {e}")
            # 使用默认装备作为后备方案
            self.equipment["武器"] = {
                "name": "木剑",
                "type": "武器",
                "level": 1,
                "attack": [2, 5],
                "tier": "普通",
                "price": 10
            }

            # 根据性别选择默认防具名称
            armor_name = "布衣(男)" if self.gender == "男" else "布衣(女)"
            self.equipment["防具"] = {
                "name": armor_name,
                "type": "防具",
                "level": 1,
                "defense": [1, 3],
                "tier": "普通",
                "price": 10
            }

    def calculate_required_exp(self, level):
        """计算升级所需经验"""
        # 记录日志以便调试
        logger.debug(f"计算等级 {level} 所需经验值")

        exp_table = {
            1: 100,
            2: 200,
            3: 300,
            4: 400,
            5: 600,
            6: 900,
            7: 1200,
            8: 1700,
            9: 2500,
            10: 6000,
            11: 8000,
            12: 10000,
            13: 15000,
            14: 30000,
            15: 40000,
            16: 50000,
            17: 70000,
            18: 100000,
            19: 120000,
            20: 250000,
            21: 300000,
            22: 350000,
            23: 400000,
            24: 500000,
            25: 700000,
            26: 1000000,
            27: 1400000,
            28: 1800000,
            29: 2000000,
            30: 2400000,
            31: 2800000,
            32: 3200000,
            33: 3600000,
            34: 4000000,
            35: 4800000,
            36: 5600000,
            37: 8200000,
            38: 9000000,
            39: 12000000,
            40: 16000000,
            41: 30000000,
            42: 50000000,
            43: 80000000,
            44: 120000000,
            45: 480000000,
            46: 360000000,
            47: 400000000,
            48: 420000000,
            49: 430000000,
            50: 440000000,
            51: 460000000,
            52: 480000000,
            53: 500000000,
            54: 520000000,
            55: 550000000,
            56: 600000000,
            57: 700000000,
            58: 800000000,
            59: 900000000,
        }

        if level in exp_table:
            return exp_table[level]
        elif level > 59:
            # 60级以上使用固定值或计算公式
            return 1000000000  # 10亿经验
        else:
            # 默认情况，使用基础公式
            return 100 * level

    @property
    def exp(self):
        return self._exp

    @exp.setter
    def exp(self, value):
        self._exp = max(0, value)

    def gain_exp(self, exp):
        """获得经验值"""
        try:
            # 确保经验值是有效的数值
            if not isinstance(exp, (int, float)) or exp < 0:
                logger.warning(f"无效的经验值: {exp}")
                return

            # 增加经验值
            old_exp = self.exp
            self.exp += exp

            # 打印调试信息
            logger.debug(f"获得经验: +{exp}, 当前: {old_exp} -> {self.exp}, 所需: {self.required_exp}")

            # 记录当前等级信息
            if self.game:
                current_level = self.level
                current_required = self.required_exp
                self.game.add_log(f"获得经验: +{exp}, 当前: {self.exp}/{current_required}, 等级: {current_level}")

            # 检查是否升级
            while self.exp >= self.required_exp:
                self.level_up()

        except Exception as e:
            logger.error(f"获得经验值时出错: {e}")

    def take_damage(self, damage):
        """受到伤害"""
        try:
            # 确保 damage 是有效的数值
            if not isinstance(damage, (int, float)) or damage < 0:
                logger.warning(f"无效的伤害值: {damage}")
                return 0

            # 计算实际伤害（考虑防御力）
            actual_damage = max(1, damage - self.defense)
            logger.debug(f"受到伤害: 原始={damage}, 防御={self.defense}, 实际={actual_damage}")

            # 应用伤害减免效果（如魔法盾）
            if hasattr(self, "buffs"):
                damage_reduction = 0
                current_time = time.time()

                # 检查是否有伤害减免buff
                for buff_id, buff in list(self.buffs.items()):
                    if buff["type"] == "damage_reduction":
                        # 检查buff是否过期
                        if current_time - buff["start_time"] < buff["duration"]:
                            # 应用伤害减免
                            damage_reduction = max(damage_reduction, buff["value"])
                            logger.debug(f"应用伤害减免: {buff['skill_name']}, 减免比例: {buff['value']*100}%")
                        else:
                            # 移除过期buff
                            del self.buffs[buff_id]
                            logger.info(f"伤害减免buff已过期: {buff_id}")

                if damage_reduction > 0:
                    # 应用伤害减免到最终伤害
                    reduced_damage = int(actual_damage * (1 - damage_reduction))
                    logger.debug(f"伤害减免后: {actual_damage} -> {reduced_damage} (减免: {damage_reduction*100}%)")
                    actual_damage = max(1, reduced_damage)  # 确保至少造成1点伤害

            # 减少生命值
            current_hp = self.hp
            new_hp = max(0, current_hp - actual_damage)
            self.hp = new_hp

            # 检查是否死亡
            if new_hp <= 0:
                self.is_dead = True
                self.last_death_time = time.time()
                logger.info(f"玩家死亡! 剩余生命值: {new_hp}")
                if self.game:
                    self.game.handle_player_death()

            # 返回实际伤害值
            return actual_damage

        except Exception as e:
            logger.error(f"处理玩家伤害时出错: {e}")
            # 发生错误时确保玩家不会意外死亡
            self.hp = max(0, self.hp)
            return 0

    def level_up(self):
        """处理升级时的属性提升"""
        # 计算新的等级
        new_level = self.level + 1

        # 打印调试信息
        old_exp = self.exp
        old_required = self.required_exp

        # 更新等级
        self.level = new_level

        # 获取新等级的基础属性
        base_stats = get_level_stats(self.character_class, new_level)

        # 更新属性
        old_max_hp = self.max_hp
        old_max_mp = self.max_mp
        old_attack = self.attack
        old_defense = self._defense  # 使用_defense而不是defense属性，因为defense属性包含装备加成

        # 更新属性
        self.max_hp = base_stats[0]
        self.hp = self.max_hp  # 升级时恢复满血
        self.max_mp = base_stats[1]
        self.mp = self.max_mp  # 升级时恢复满蓝

        # 使用属性表中的攻击下限和上限的平均值作为基础攻击力
        if len(base_stats) > 3:
            attack_min = base_stats[2]
            attack_max = base_stats[3]
            self.attack = (attack_min + attack_max) // 2
        else:
            self.attack = base_stats[2]

        # 使用属性表中的防御下限和上限的平均值作为基础防御力
        if len(base_stats) > 5:
            defense_min = base_stats[4]
            defense_max = base_stats[5]
            self.defense = (defense_min + defense_max) // 2
        elif len(base_stats) > 4:
            self.defense = base_stats[4]
        else:
            self.defense = 0

        # 更新魔法防御
        if len(base_stats) > 6:
            self._magic_defense = base_stats[6]

        # 更新攻击速度
        if len(base_stats) > 11:
            self.attack_speed = base_stats[11]
        else:
            logger.warning(f"等级 {new_level} 的基础属性列表长度不足，无法获取攻速")
            self.attack_speed = 1.0

        # 更新所需经验值 - 将这行放在属性更新后
        new_required = self.calculate_required_exp(new_level)
        self.required_exp = new_required

        # 记录日志
        logger.debug(f"  升级: {new_level-1} -> {new_level}")
        logger.debug(f"  经验: {old_exp}/{old_required} -> {self.exp}/{new_required}")
        logger.debug(f"  生命值: {old_max_hp} -> {self.max_hp} (+{self.max_hp - old_max_hp})")
        logger.debug(f"  魔法值: {old_max_mp} -> {self.max_mp} (+{self.max_mp - old_max_mp})")
        logger.debug(f"  攻击力: {old_attack} -> {self.attack} (+{self.attack - old_attack})")
        logger.debug(f"  防御力: {old_defense} -> {self._defense} (+{self._defense - old_defense})")

        # 添加升级日志
        if self.game:
            self.game.add_log(f"恭喜升级！当前等级：{new_level}, 下级所需经验: {new_required}")
            self.game.add_log(f"生命值提升: +{self.max_hp - old_max_hp}, 攻击力提升: +{self.attack - old_attack}, 防御力提升: +{self._defense - old_defense}")

    # 属性访问器
    @property
    def level(self):
        return self._level

    @level.setter
    def level(self, value):
        self._level = max(1, value)

    @property
    def required_exp(self):
        """获取升级所需经验"""
        return self.calculate_required_exp(self.level)

    @required_exp.setter
    def required_exp(self, value):
        self._required_exp = value

    @property
    def hp(self):
        return self._hp

    @hp.setter
    def hp(self, value):
        self._hp = max(0, min(self._max_hp, value))

    @property
    def max_hp(self):
        return self._max_hp

    @max_hp.setter
    def max_hp(self, value):
        self._max_hp = max(1, value)

    @property
    def attack(self):
        # 基础攻击力
        base_attack = self._attack

        # 获取装备加成
        equipment_bonus = self.get_equipment_bonus()
        equipment_attack = 0
        if "attack" in equipment_bonus:
            # 与magic和taoism属性一致，使用最大值
            attack_min, attack_max = equipment_bonus["attack"]
            equipment_attack = attack_max

        # 返回基础攻击力加装备加成
        total_attack = base_attack + equipment_attack
        logger.debug(f"获取攻击力: 基础={base_attack}, 装备加成={equipment_attack}, 总计={total_attack}")
        return total_attack

    @attack.setter
    def attack(self, value):
        self._attack = max(1, value)

    @property
    def defense(self):
        # 使用缓存机制避免频繁计算
        if hasattr(self, '_cached_defense') and hasattr(self, '_defense_cache_time'):
            # 检查缓存是否在有效期内 (100ms内不重新计算)
            current_time = time.time()
            if current_time - self._defense_cache_time < 0.1:
                return self._cached_defense

        # 需要重新计算时执行原始逻辑
        base_defense = self._defense
        equipment_bonus = self.get_equipment_bonus()
        defense_bonus_max = equipment_bonus["defense"][1]  # 使用最大值
        total_defense = base_defense + defense_bonus_max

        # 只在重新计算时记录详细日志，避免日志过多
        logger.debug(f"防御计算: 基础防御={base_defense}, 装备加成max={defense_bonus_max}, 总防御={total_defense}")

        # 更新缓存
        self._cached_defense = total_defense
        self._defense_cache_time = time.time()

        return total_defense

    @defense.setter
    def defense(self, value):
        self._defense = max(0, value)
        # 清除缓存，确保下次获取时重新计算
        if hasattr(self, '_cached_defense'):
            delattr(self, '_cached_defense')
        if hasattr(self, '_defense_cache_time'):
            delattr(self, '_defense_cache_time')

    @property
    def gold(self):
        """获取金币"""
        return self._gold

    @gold.setter
    def gold(self, value):
        """设置金币"""
        self._gold = max(0, value)

    @property
    def yuanbao(self):
        """获取元宝"""
        return self._yuanbao

    @yuanbao.setter
    def yuanbao(self, value):
        if value < 0:
            value = 0
        self._yuanbao = value

    @property
    def mp(self):
        return self._mp

    @mp.setter
    def mp(self, value):
        self._mp = max(0, min(self._max_mp, value))

    @property
    def max_mp(self):
        return self._max_mp

    @max_mp.setter
    def max_mp(self, value):
        self._max_mp = max(1, value)

    @property
    def attack_speed(self):
        return self._attack_speed

    @attack_speed.setter
    def attack_speed(self, value):
        """设置攻击速度并更新攻击间隔"""
        try:
            # 确保value是有效的数值
            value = float(value)
            if value <= 0:
                logger.warning(f"无效的攻击速度值: {value}，使用默认值1.0")
                value = 1.0

            self._attack_speed = value

            # 导入全局战斗速度修正因子
            from core.config import GameConfig

            # 应用全局战斗速度修正 - 降低战斗速度
            modified_speed = value * GameConfig.BATTLE_SPEED_MODIFIER

            # 更新攻击间隔
            self.attack_interval = 1.0 / modified_speed
            logger.debug(f"更新攻击速度: 原始={value}, 修正后={modified_speed}, 攻击间隔: {self.attack_interval}")
        except (ValueError, TypeError) as e:
            logger.error(f"设置攻击速度失败: {e}")
            self._attack_speed = 1.0
            self.attack_interval = 1.0 / GameConfig.BATTLE_SPEED_MODIFIER  # 应用全局速度修正

    @property
    def crit_rate(self):
        # 只计算装备提供的暴击率
        equipment_bonus = self.get_equipment_bonus()
        crit_rate_value = equipment_bonus.get("crit_rate", 0)
        logger.debug(f"获取暴击率: {crit_rate_value}%")
        return crit_rate_value  # 直接返回百分比形式的暴击率

    @crit_rate.setter
    def crit_rate(self, value):
        # 由于暴击完全由装备决定，这个setter实际上不会改变任何值
        logger.warning("暴击率现在完全由装备决定，无法直接设置")
        pass

    def get_base_accuracy(self):
        """获取玩家基础准确性加上被动技能加成"""
        # 使用缓存机制避免频繁计算
        if hasattr(self, '_cached_accuracy') and hasattr(self, '_accuracy_cache_time'):
            # 检查缓存是否在有效期内 (100ms内不重新计算)
            current_time = time.time()
            if current_time - self._accuracy_cache_time < 0.1:
                return self._cached_accuracy

        # 需要重新计算时执行原始逻辑
        base = self._base_accuracy
        # 获取被动技能加成
        passive_effects = self.get_passive_skill_effects()
        total_accuracy = base + passive_effects.get("accuracy", 0)

        # 更新缓存
        self._cached_accuracy = total_accuracy
        self._accuracy_cache_time = time.time()

        return total_accuracy

    @property
    def base_accuracy(self):
        """获取玩家准确性"""
        try:
            accuracy = self.get_base_accuracy()
            # 只有在调试时记录日志，减少日志量
            if accuracy <= 0:
                logger.warning("无效的准确性值，使用默认值10")
                return 10
            return accuracy
        except Exception as e:
            logger.error(f"获取准确性出错: {e}")
            return 10

    @base_accuracy.setter
    def base_accuracy(self, value):
        """设置玩家准确性"""
        try:
            value = int(value)
            if value <= 0:
                logger.warning(f"尝试设置无效的准确性值: {value}，使用默认值10")
                value = 10
            self._base_accuracy = value
            # 清除缓存，确保下次获取时重新计算
            if hasattr(self, '_cached_accuracy'):
                delattr(self, '_cached_accuracy')
            if hasattr(self, '_accuracy_cache_time'):
                delattr(self, '_accuracy_cache_time')
            logger.debug(f"设置玩家准确性: {value}")
        except Exception as e:
            logger.error(f"设置准确性出错: {e}")
            self._base_accuracy = 10

    @property
    def agility(self):
        """获取玩家敏捷性"""
        try:
            agility = self._agility
            logger.debug(f"获取玩家敏捷性: {agility}")
            if agility <= 0:
                logger.warning("无效的敏捷性值，使用默认值5")
                return 5
            return agility
        except Exception as e:
            logger.error(f"获取敏捷性出错: {e}")
            return 5

    @agility.setter
    def agility(self, value):
        """设置玩家敏捷性"""
        try:
            value = int(value)
            if value <= 0:
                logger.warning(f"尝试设置无效的敏捷性值: {value}，使用默认值5")
                value = 5
            self._agility = value
            logger.debug(f"设置玩家敏捷性: {value}")
        except Exception as e:
            logger.error(f"设置敏捷性出错: {e}")
            self._agility = 5

    @property
    def luck(self):
        """获取玩家总幸运值（基础+装备）

        幸运值影响攻击伤害波动范围，幸运值越高，造成最大伤害的概率越大：
        幸运值0: 10%几率造成最大伤害
        幸运值1: 11.1%几率造成最大伤害
        幸运值2: 12.5%几率造成最大伤害
        幸运值3: 14.3%几率造成最大伤害
        幸运值4: 16.7%几率造成最大伤害
        幸运值5: 20%几率造成最大伤害
        幸运值6: 25%几率造成最大伤害
        幸运值7: 33%几率造成最大伤害
        幸运值8: 50%几率造成最大伤害
        幸运值≥9: 100%几率造成最大伤害
        """
        equipment_bonus = self.get_equipment_bonus()
        equipment_luck = equipment_bonus.get("luck", 0)
        total_luck = self._base_luck + equipment_luck
        return total_luck

    @luck.setter
    def luck(self, value):
        """设置玩家幸运值（基础值）
        幸运值影响攻击伤害计算，值越高，造成最大伤害的概率越大。
        幸运值达到9或以上时，玩家将始终造成最大伤害。
        """
        # 此方法已被重写，现在设置基础幸运值
        self._base_luck = value
        # 兼容性考虑，同时设置_luck
        self._luck = value

    @property
    def base_luck(self):
        """获取玩家基础幸运值（不包含装备加成）

        幸运值影响攻击伤害波动范围，幸运值越高，造成最大伤害的概率越大。
        幸运值达到9或以上时，玩家将始终造成最大伤害。
        详细概率见luck属性文档。
        """
        return self._base_luck

    @base_luck.setter
    def base_luck(self, value):
        """设置玩家基础幸运值

        幸运值影响攻击伤害计算，值越高，造成最大伤害的概率越大。
        """
        self._base_luck = value

    @property
    def magic_defense(self):
        """获取玩家魔法防御"""
        try:
            magic_defense = self._magic_defense
            logger.debug(f"获取玩家魔法防御: {magic_defense}")
            if magic_defense < 0:
                logger.warning("无效的魔法防御值，使用默认值0")
                return 0
            return magic_defense
        except Exception as e:
            logger.error(f"获取魔法防御出错: {e}")
            return 0

    @magic_defense.setter
    def magic_defense(self, value):
        """设置玩家魔法防御"""
        try:
            value = int(value)
            if value < 0:
                logger.warning(f"尝试设置无效的魔法防御值: {value}，使用默认值0")
                value = 0
            self._magic_defense = value
            logger.debug(f"设置玩家魔法防御: {value}")
        except Exception as e:
            logger.error(f"设置魔法防御出错: {e}")
            self._magic_defense = 0

    @property
    def magic_dodge(self):
        """获取玩家魔法闪避率"""
        try:
            magic_dodge = self._magic_dodge
            logger.debug(f"获取玩家魔法闪避率: {magic_dodge}")
            if magic_dodge < 0:
                logger.warning("无效的魔法闪避率，使用默认值0")
                return 0
            return magic_dodge
        except Exception as e:
            logger.error(f"获取魔法闪避率出错: {e}")
            return 0

    @magic_dodge.setter
    def magic_dodge(self, value):
        """设置玩家魔法闪避率"""
        try:
            value = int(value)
            if value < 0:
                logger.warning(f"尝试设置无效的魔法闪避率: {value}，使用默认值0")
                value = 0
            self._magic_dodge = value
            logger.debug(f"设置玩家魔法闪避率: {value}")
        except Exception as e:
            logger.error(f"设置魔法闪避率出错: {e}")
            self._magic_dodge = 0

    # 在这里添加VIP相关属性访问器
    @property
    def vip_level(self):
        """获取VIP等级"""
        return self._vip_level

    @vip_level.setter
    def vip_level(self, value):
        """设置VIP等级"""
        self._vip_level = max(0, min(6, value))  # VIP等级范围为0-6

    @property
    def vip_activated(self):
        """获取已激活的VIP等级"""
        return self._vip_activated

    def get_vip_drop_rate_bonus(self):
        """获取VIP对装备掉落率的加成
        每VIP等级提供10%的掉落率加成，例如VIP1提供10%，VIP6提供60%

        Returns:
            float: 加成系数，例如1.1表示增加10%
        """
        if self._vip_level <= 0:
            return 1.0

        # 每个VIP等级提供10%的掉落率加成
        bonus = 1.0 + (self._vip_level * 0.1)
        logger.debug(f"VIP{self._vip_level}提供掉落率加成: {bonus:.1f}倍")
        return bonus

    def get_vip_sell_bonus(self):
        """获取VIP对装备售卖的金币加成
        每VIP等级提供20%的售卖加成，例如VIP1提供20%，VIP6提供120%

        Returns:
            float: 加成系数，例如1.2表示增加20%
        """
        if self._vip_level <= 0:
            return 1.0

        # 每个VIP等级提供20%的售卖金币加成
        bonus = 1.0 + (self._vip_level * 0.2)
        logger.debug(f"VIP{self._vip_level}提供售卖金币加成: {bonus:.1f}倍")
        return bonus

    def activate_vip(self, target_level):
        """激活指定等级的VIP

        Args:
            target_level: 要激活的VIP等级(1-6)

        Returns:
            bool: 激活是否成功
        """
        # 检查参数有效性
        if not isinstance(target_level, int) or target_level < 1 or target_level > 6:
            logger.warning(f"激活VIP失败：无效的VIP等级 {target_level}")
            return False

        # 检查是否已经激活过该等级
        if target_level in self._vip_activated:
            logger.warning(f"VIP{target_level}已经激活过，无需重复激活")
            return False

        # 根据VIP等级计算所需元宝
        vip_costs = {
            1: 30,    # VIP1需要30元宝
            2: 100,   # VIP2需要100元宝
            3: 500,   # VIP3需要500元宝
            4: 1000,  # VIP4需要1000元宝
            5: 3000,  # VIP5需要3000元宝
            6: 5000   # VIP6需要5000元宝
        }

        required_yuanbao = vip_costs.get(target_level, 0)

        # 检查元宝是否足够
        if self._yuanbao < required_yuanbao:
            logger.warning(f"激活VIP{target_level}失败：元宝不足，需要{required_yuanbao}，当前{self._yuanbao}")
            return False

        # 扣除元宝
        self._yuanbao -= required_yuanbao

        # 添加到已激活列表
        self._vip_activated.append(target_level)

        # 更新VIP等级为当前已激活的最高等级
        self._vip_level = max(self._vip_activated)

        logger.info(f"成功激活VIP{target_level}，已扣除{required_yuanbao}元宝，当前VIP等级：{self._vip_level}")
        return True

    def save_data(self):
        """保存玩家数据为字典"""
        try:
            basic_data = {
                "character_class": self.character_class,
                "name": self.name,
                "gender": self.gender,

                # 等级和经验
                "level": self._level,
                "exp": self._exp,
                "required_exp": self.required_exp,

                # 货币
                "gold": self._gold,
                "yuanbao": self._yuanbao,

                # VIP数据
                "vip_level": self._vip_level,
                "vip_activated": self._vip_activated,
                "refresh_tokens": self._refresh_tokens,

                # 生命值和魔法值
                "hp": self._hp,
                "max_hp": self._max_hp,
                "mp": self._mp,
                "max_mp": self._max_mp,

                # 战斗属性
                "attack": self._attack,
                "defense": self._defense,
                "attack_speed": self._attack_speed,
                "crit_rate": self._crit_rate,
                "base_accuracy": self._base_accuracy,
                "agility": self._agility,
                "luck": self._luck,
                "magic_defense": self._magic_defense,
                "magic_dodge": self._magic_dodge
            }

            # 确保装备是一个有效的字典
            if hasattr(self, "equipment") and isinstance(self.equipment, dict):
                basic_data["equipment"] = self.equipment
            else:
                basic_data["equipment"] = {}

            # 确保背包是一个有效的列表
            if hasattr(self, "inventory") and isinstance(self.inventory, list):
                basic_data["inventory"] = self.inventory
            else:
                basic_data["inventory"] = []

            # 确保仓库是一个有效的列表
            if hasattr(self, "storage") and isinstance(self.storage, list):
                basic_data["storage"] = self.storage
            else:
                basic_data["storage"] = []

            # 加载锁定物品数据
            if hasattr(self, "locked_items") and isinstance(self.locked_items, set):
                basic_data["locked_items"] = list(self.locked_items)
            else:
                basic_data["locked_items"] = []

            logger.info(f"玩家数据保存成功，包含字段: {', '.join(basic_data.keys())}")
            return basic_data

        except Exception as e:
            logger.error(f"保存玩家数据时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 即使出错也返回最小可用数据
            return {
                "character_class": "战士",
                "name": "数据恢复玩家",
                "level": 1,
                "hp": 100,
                "max_hp": 100,
                "attack": 10,
                "defense": 5,
                "equipment": {},
                "inventory": [],
                "storage": [],
                "locked_items": [],
                "gold": 0,
                "yuanbao": 5
            }

    def load_data(self, data):
        """从字典加载玩家数据，兼容Game.load_game方法

        参数:
            data: 玩家数据字典

        返回:
            bool: 是否成功加载
        """
        try:
            # 检查数据有效性
            if not data or not isinstance(data, dict):
                logger.error(f"无效的玩家数据: {type(data)}")
                return False

            # 调用现有的load_from_dict方法
            self.load_from_dict(data)
            logger.info(f"玩家数据加载成功: {self.name}, {self.character_class}, 等级{self.level}")
            return True
        except Exception as e:
            logger.error(f"加载玩家数据时出错: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def calculate_equipment_bonus(self):
        """计算所有装备加成的总和"""
        try:
            # 清除防御缓存
            if hasattr(self, '_cached_defense'):
                delattr(self, '_cached_defense')
            if hasattr(self, '_defense_cache_time'):
                delattr(self, '_defense_cache_time')

            # 清除准确性缓存
            if hasattr(self, '_cached_accuracy'):
                delattr(self, '_cached_accuracy')
            if hasattr(self, '_accuracy_cache_time'):
                delattr(self, '_accuracy_cache_time')

            # 清除魔法攻击缓存
            if hasattr(self, '_cached_magic_attack'):
                delattr(self, '_cached_magic_attack')
            if hasattr(self, '_magic_attack_cache_time'):
                delattr(self, '_magic_attack_cache_time')

            # 初始化加成属性
            bonus = {
                "attack": [0, 0],  # [最小值, 最大值]
                "defense": [0, 0],
                "magic": [0, 0],
                "taoism": [0, 0],
                "magic_defense": [0, 0],
                "accuracy": 0,
                "agility": 0,
                "luck": 0,
                "magic_dodge": 0,
                "crit_rate": 0,  # 添加暴击率属性
                "attack_speed": 0,  # 添加攻速属性
                "lifesteal": 0,   # 添加吸血属性(百分比)
                "flat_lifesteal": 0  # 添加固定值吸血属性
            }

            # 遍历所有装备槽位
            for slot, item in self.equipment.items():
                if item is None:
                    continue

                # 确保item是字典类型
                if not isinstance(item, dict):
                    logger.warning(f"装备槽位 '{slot}' 的值不是字典类型: {type(item)}")
                    continue

                # 记录计算
                if "name" in item:
                    logger.debug(f"计算装备加成 - 槽位: {slot}, 装备: {item['name']}")
                else:
                    logger.debug(f"计算装备加成 - 槽位: {slot}, 装备: {item}")

                # 攻击力加成
                if "attack" in item:
                    attack_bonus = item["attack"]
                    if isinstance(attack_bonus, (list, tuple)) and len(attack_bonus) >= 2:
                        bonus["attack"][0] += attack_bonus[0]
                        bonus["attack"][1] += attack_bonus[1]
                    else:
                        bonus["attack"][0] += attack_bonus
                        bonus["attack"][1] += attack_bonus

                # 防御力加成
                if "defense" in item:
                    defense_bonus = item["defense"]
                    if isinstance(defense_bonus, (list, tuple)) and len(defense_bonus) >= 2:
                        bonus["defense"][0] += defense_bonus[0]
                        bonus["defense"][1] += defense_bonus[1]
                    else:
                        bonus["defense"][0] += defense_bonus
                        bonus["defense"][1] += defense_bonus

                # 魔法加成
                if "magic" in item:
                    magic_bonus = item["magic"]
                    if isinstance(magic_bonus, (list, tuple)) and len(magic_bonus) >= 2:
                        bonus["magic"][0] += magic_bonus[0]
                        bonus["magic"][1] += magic_bonus[1]
                    else:
                        bonus["magic"][0] += magic_bonus
                        bonus["magic"][1] += magic_bonus

                # 道术加成
                if "taoism" in item:
                    taoism_bonus = item["taoism"]
                    if isinstance(taoism_bonus, (list, tuple)) and len(taoism_bonus) >= 2:
                        bonus["taoism"][0] += taoism_bonus[0]
                        bonus["taoism"][1] += taoism_bonus[1]
                    else:
                        bonus["taoism"][0] += taoism_bonus
                        bonus["taoism"][1] += taoism_bonus

                # 魔法防御加成
                if "magic_defense" in item:
                    magic_def_bonus = item["magic_defense"]
                    if isinstance(magic_def_bonus, (list, tuple)) and len(magic_def_bonus) >= 2:
                        bonus["magic_defense"][0] += magic_def_bonus[0]
                        bonus["magic_defense"][1] += magic_def_bonus[1]
                    else:
                        bonus["magic_defense"][0] += magic_def_bonus
                        bonus["magic_defense"][1] += magic_def_bonus

                # 单值属性加成
                if "accuracy" in item:
                    bonus["accuracy"] += item["accuracy"]
                if "agility" in item:
                    bonus["agility"] += item["agility"]
                if "luck" in item:
                    if isinstance(item["luck"], str) and item["luck"] == "random":
                        # 如果luck属性是"random"，则按指定概率生成0-2之间的随机幸运值
                        # 0的概率为85%，1的概率为14%，2的概率为1%
                        import random
                        luck_values = [0, 1, 2]
                        luck_weights = [85, 14, 1]
                        random_luck = random.choices(luck_values, weights=luck_weights, k=1)[0]
                        bonus["luck"] += random_luck
                        logger.debug(f"装备 {item.get('name', '未知')} 生成随机幸运值: {random_luck}")
                    else:
                        bonus["luck"] += item["luck"]
                if "magic_dodge" in item:
                    bonus["magic_dodge"] += item["magic_dodge"]
                if "crit_rate" in item:
                    bonus["crit_rate"] += item["crit_rate"]

                # 攻击速度加成 - 仅适用于武器槽位
                if slot == "武器" and "attack_speed" in item:
                    bonus["attack_speed"] += item["attack_speed"]
                    logger.debug(f"武器{item.get('name', '未知')}提供攻速加成: +{item['attack_speed']}")

                # 吸血属性 - 应用于所有装备
                if "lifesteal" in item:
                    bonus["lifesteal"] += item["lifesteal"]
                    logger.debug(f"装备{item.get('name', '未知')}提供吸血加成: +{item['lifesteal']}%")

                # 固定数值吸血 - 应用于所有装备
                if "flat_lifesteal" in item:
                    bonus["flat_lifesteal"] += item["flat_lifesteal"]
                    logger.debug(f"装备{item.get('name', '未知')}提供固定吸血: +{item['flat_lifesteal']}点")

            # 记录装备状态，避免不必要的重复计算
            self._last_equipment_state = {k: (v["name"] if isinstance(v, dict) and "name" in v else v) for k, v in self.equipment.items()}
            self._equipment_bonus = bonus

            logger.debug(f"计算装备加成: {bonus}")
            return bonus

        except Exception as e:
            logger.error(f"计算装备加成时出错: {e}\n{traceback.format_exc()}")
            return {
                "attack": [0, 0],
                "defense": [0, 0],
                "magic": [0, 0],
                "taoism": [0, 0],
                "magic_defense": [0, 0],
                "accuracy": 0,
                "agility": 0,
                "luck": 0,
                "magic_dodge": 0,
                "crit_rate": 0,
                "attack_speed": 0,
                "lifesteal": 0,
                "flat_lifesteal": 0
            }

    def get_equipment_bonus(self):
        """获取装备加成，如果装备变化则重新计算"""
        current_equipment = {k: (v["name"] if isinstance(v, dict) and "name" in v else v) for k, v in self.equipment.items()}

        # 检查装备是否变化
        if self._last_equipment_state != current_equipment:
            return self.calculate_equipment_bonus()

        return self._equipment_bonus

    def get_damage_range(self):
        """获取玩家的伤害范围"""
        try:
            # 获取基础攻击力(现在是没有考虑装备的原始攻击力)
            base_attack = self._attack

            # 获取装备加成
            equipment_bonus = self.get_equipment_bonus()
            attack_bonus_min = equipment_bonus["attack"][0]
            attack_bonus_max = equipment_bonus["attack"][1]

            # 计算伤害范围
            min_damage = int((base_attack + attack_bonus_min) * 0.9)
            max_damage = int((base_attack + attack_bonus_max) * 1.1)

            # 确保至少造成1点伤害
            return max(1, min_damage), max(1, max_damage)
        except Exception as e:
            logger.error(f"计算伤害范围时出错: {e}")
            return 1, 1  # 出错时返回最小伤害

    def calculate_damage(self):
        """计算攻击伤害

        返回:
            tuple: (伤害值, 是否暴击)
        """
        try:
            # 获取攻击范围
            min_damage, max_damage = self.get_damage_range()

            # 获取玩家幸运值
            player_luck = self.luck

            # 根据幸运值确定最大伤害的概率
            max_damage_probabilities = {
                0: 0.1,     # 10%
                1: 1/9,     # 11.1%
                2: 1/8,     # 12.5%
                3: 1/7,     # 14.3%
                4: 1/6,     # 16.7%
                5: 0.2,     # 20%
                6: 0.25,    # 25%
                7: 1/3,     # 33%
                8: 0.5,     # 50%
            }

            # 如果幸运值≥9，必定造成最大伤害
            if player_luck >= 9:
                damage = max_damage
                logger.debug(f"幸运值{player_luck}≥9，造成最大伤害: {damage}")
            else:
                # 获取对应幸运值的最大伤害概率
                max_damage_prob = max_damage_probabilities.get(player_luck, 0.1)  # 默认为10%

                # 随机决定是否造成最大伤害
                if random.random() < max_damage_prob:
                    damage = max_damage
                    logger.debug(f"幸运值{player_luck}触发最大伤害({max_damage_prob*100:.1f}%概率)")
                else:
                    # 如果不是最大伤害，在最小值和最大值之间随机
                    damage = random.randint(min_damage, max_damage)
                    logger.debug(f"幸运值{player_luck}下的普通伤害: {damage} (范围: {min_damage}-{max_damage})")

            # 获取装备暴击率
            equipment_bonus = self.get_equipment_bonus()
            crit_rate = equipment_bonus.get("crit_rate", 0)

            # 暴击判定
            rand_value = random.random()
            is_crit = rand_value < crit_rate

            # 暴击加成
            if is_crit:
                damage = int(damage * 1.5)  # 暴击增加50%伤害

            # 获取被动技能加成
            passive_effects = self.get_passive_skill_effects()

            # 攻杀剑术 - 伤害百分比加成
            if "damage_percent" in passive_effects and passive_effects["damage_percent"] > 0:
                percent_bonus = passive_effects["damage_percent"] / 100.0
                damage = int(damage * (1 + percent_bonus))

            # 刺杀剑术 - 额外伤害加成
            if "extra_damage" in passive_effects and passive_effects["extra_damage"] > 0:
                extra_percent = passive_effects["extra_damage"] / 100.0
                extra_damage = int(damage * extra_percent)
                damage += extra_damage

            logger.debug(f"计算伤害: 基础={min_damage}-{max_damage}, 最终={damage}, 暴击={is_crit}, 暴击率={crit_rate:.2f}, 随机值={rand_value:.2f}, 幸运值={player_luck}")
            return (damage, is_crit)
        except Exception as e:
            logger.error(f"计算伤害时出错: {e}")
            return (1, False)  # 出错时返回最小伤害

    def get_item_info(self, item_name):
        """获取物品信息

        Args:
            item_name: 物品名称或物品字典

        Returns:
            Dict: 物品信息字典，如果不存在则返回None
        """
        try:
            # 如果item_name是字典，直接获取名称
            if isinstance(item_name, dict):
                name = item_name.get("name", "")
                # 如果字典已经包含了必要信息，直接返回
                if name and "type" in item_name:
                    return item_name
            else:
                # 如果是字符串，直接使用
                name = str(item_name)

            # 检查装备数据库
            equipment_db = GameConfig.loaded_equipment.get("equipment_db", {})

            # 在各装备列表中查找
            for category, items in equipment_db.items():
                for item in items:
                    if item.get("name") == name:
                        # 确保物品有类型信息
                        result = item.copy()
                        if "type" not in result or not result["type"]:
                            # 根据类别名推断类型
                            if category == "武器":
                                result["type"] = "武器"
                            elif category == "防具" or category == "盔甲":
                                result["type"] = "防具"
                            elif category == "头盔":
                                result["type"] = "头盔"
                            elif category == "项链":
                                result["type"] = "项链"
                            elif category == "手镯":
                                result["type"] = "手镯"
                            elif category == "戒指":
                                result["type"] = "戒指"
                            else:
                                # 根据物品名称推断类型
                                if "剑" in name or "刀" in name or "杖" in name or "斧" in name:
                                    result["type"] = "武器"
                                elif "盔甲" in name or "战衣" in name or "布衣" in name or "袍" in name:
                                    result["type"] = "防具"
                                elif "项链" in name or "明珠" in name or "珠子" in name or "金项链" in name:
                                    result["type"] = "项链"
                                elif "戒指" in name:
                                    result["type"] = "戒指"
                                elif "手镯" in name or "护腕" in name or "手套" in name:
                                    result["type"] = "手镯"
                                elif "头盔" in name or "帽子" in name:
                                    result["type"] = "头盔"
                                else:
                                    result["type"] = "其他"
                        return result

            # 如果在数据库中找不到，尝试在已装备物品中查找
            for slot, equipped_item in self.equipment.items():
                if equipped_item and (
                    (isinstance(equipped_item, dict) and equipped_item.get("name") == name) or
                    str(equipped_item) == name
                ):
                    # 如果是字符串，转换为字典
                    if not isinstance(equipped_item, dict):
                        result = {"name": name}
                    else:
                        result = equipped_item.copy()

                    # 确保装备有类型信息
                    if "type" not in result or not result["type"]:
                        # 从装备槽位推断类型
                        if slot == "武器":
                            result["type"] = "武器"
                        elif slot == "防具":
                            result["type"] = "防具"
                        elif slot == "头盔":
                            result["type"] = "头盔"
                        elif slot == "项链":
                            result["type"] = "项链"
                        elif slot in ["左手镯", "右手镯"]:
                            result["type"] = "手镯"
                        elif slot in ["左戒指", "右戒指"]:
                            result["type"] = "戒指"
                        else:
                            result["type"] = "其他"
                    return result

            # 如果都找不到，根据名称推断类型创建基本物品信息
            if name:
                # 根据物品名称推断类型
                item_type = "其他"
                if "药" in name or "药水" in name or "丹" in name:
                    item_type = "消耗品"
                elif "任务" in name or "信" in name or "书" in name:
                    item_type = "任务物品"
                elif "剑" in name or "刀" in name or "杖" in name or "斧" in name or "匕首" in name:
                    item_type = "武器"
                elif "盔甲" in name or "战衣" in name or "布衣" in name or "袍" in name:
                    item_type = "防具"
                elif "项链" in name or "明珠" in name or "珠子" in name or "金项链" in name:
                    item_type = "项链"
                elif "戒指" in name:
                    item_type = "戒指"
                elif "手镯" in name or "护腕" in name or "手套" in name:
                    item_type = "手镯"
                elif "头盔" in name or "帽子" in name:
                    item_type = "头盔"

                basic_item = {
                    "name": name,
                    "type": item_type,
                    "quality": "普通",
                    "level": 1,
                    "description": f"这是一个{item_type}"
                }
                logger.info(f"为找不到的物品创建基本信息: {name}, 类型: {item_type}")
                return basic_item

            logger.warning(f"未找到物品信息: {name}")
            return None

        except Exception as e:
            logger.error(f"获取物品信息时出错: {e}")
            import traceback
            logger.error(f"详细错误堆栈: {traceback.format_exc()}")
            # 如果出错但我们知道名称，尝试创建一个基本项目
            if isinstance(item_name, str) and item_name:
                return {
                    "name": item_name,
                    "type": "其他",
                    "quality": "普通",
                    "level": 1,
                    "description": "未知物品"
                }
            return None

    def recalculate_stats(self):
        """重新计算所有属性（包括装备加成）"""
        try:
            # 清除防御缓存
            if hasattr(self, '_cached_defense'):
                delattr(self, '_cached_defense')
            if hasattr(self, '_defense_cache_time'):
                delattr(self, '_defense_cache_time')

            # 清除准确性缓存
            if hasattr(self, '_cached_accuracy'):
                delattr(self, '_cached_accuracy')
            if hasattr(self, '_accuracy_cache_time'):
                delattr(self, '_accuracy_cache_time')

            # 清除魔法攻击缓存
            if hasattr(self, '_cached_magic_attack'):
                delattr(self, '_cached_magic_attack')
            if hasattr(self, '_magic_attack_cache_time'):
                delattr(self, '_magic_attack_cache_time')

            # 记录装备信息
            logger.info("开始重新计算玩家属性")
            for slot, item in self.equipment.items():
                if item:
                    if isinstance(item, dict) and "name" in item:
                        logger.info(f"  槽位 {slot}: {item['name']}")
                    else:
                        logger.info(f"  槽位 {slot}: {item}")

            # 计算装备加成
            equipment_bonus = self.calculate_equipment_bonus()

            # 处理临时buff加成
            buff_bonus = self._calculate_buff_bonus()

            # 更新防御值（应用buff加成）
            defense_buff = buff_bonus.get("defense", 0)
            if defense_buff > 0:
                logger.info(f"应用防御值buff加成: +{defense_buff}")
                self._defense += defense_buff

            # 更新魔法防御值（应用buff加成）
            magic_defense_buff = buff_bonus.get("magic_defense", 0)
            if magic_defense_buff > 0:
                logger.info(f"应用魔法防御buff加成: +{magic_defense_buff}")
                self._magic_defense += magic_defense_buff

            # 更新敏捷值（应用buff加成）
            agility_buff = buff_bonus.get("agility", 0)
            if agility_buff > 0:
                logger.info(f"应用敏捷buff加成: +{agility_buff}")
                self._agility += agility_buff

            # 更新攻击速度和攻击间隔
            attack_speed_bonus = equipment_bonus.get("attack_speed", 0)
            if attack_speed_bonus > 0:
                # 应用装备攻速加成
                original_speed = self._base_attack_speed
                new_speed = original_speed + attack_speed_bonus

                # 设置攻击速度属性
                self._attack_speed = new_speed
                logger.info(f"应用装备攻速加成: 基础{original_speed} + 装备{attack_speed_bonus} = {new_speed}")

                # 更新攻击间隔
                if new_speed > 0:
                    # 导入全局战斗速度修正因子
                    from core.config import GameConfig

                    # 应用全局战斗速度修正
                    modified_speed = new_speed * GameConfig.BATTLE_SPEED_MODIFIER
                    self.attack_interval = 1.0 / modified_speed
                    logger.info(f"更新攻击间隔: {self.attack_interval}秒 (攻速: {modified_speed})")

            logger.info("玩家属性重新计算完成")
        except Exception as e:
            logger.error(f"重新计算玩家属性时出错: {e}")
            logger.error(traceback.format_exc())

    def _calculate_buff_bonus(self):
        """计算所有buff加成

        返回:
            dict: 包含各个属性的buff加成值
        """
        bonus = {
            "defense": 0,
            "magic_defense": 0,
            "agility": 0
        }

        # 如果没有buff系统，直接返回空加成
        if not hasattr(self, "buffs") or not self.buffs:
            return bonus

        # 获取当前时间
        current_time = time.time()

        # 遍历所有buff，只计算有效的buff加成
        for buff_id, buff in list(self.buffs.items()):
            # 检查buff是否过期 - 注意：不在这里移除过期buff，而是在Game类的_update_player_buffs方法中统一处理
            if current_time - buff["start_time"] >= buff["duration"]:
                # 跳过过期的buff，但不移除
                continue

            # 应用buff效果
            buff_type = buff["type"]
            buff_value = buff["value"]

            # 记录日志，便于调试
            logger.debug(f"应用Buff加成: {buff.get('skill_name', buff_id)} - 类型: {buff_type}, 值: {buff_value}")

            if buff_type in bonus:
                bonus[buff_type] += buff_value
                logger.debug(f"累计{buff_type}加成: {bonus[buff_type]}")

        return bonus

    def update_regeneration(self, current_time):
        """更新生命值和魔法值的自动恢复

        参数:
            current_time: 当前时间戳
        """
        # 如果玩家已死亡，不进行恢复
        if self.is_dead:
            return

        # 计算距离上次恢复的时间间隔
        time_passed = current_time - self.last_regen_time

        # 计算应该恢复的点数（每秒恢复1点）
        points_to_regen = int(time_passed)  # 向下取整，以秒为单位

        # 如果有需要恢复的点数
        if points_to_regen > 0:
            # --- 新增：恢复生命值 ---
            if self.hp < self.max_hp:
                old_hp = self.hp
                self.hp = min(self.max_hp, self.hp + points_to_regen)
                actual_hp_regen = self.hp - old_hp
                if actual_hp_regen > 0:
                    logger.debug(f"玩家生命自动恢复: +{actual_hp_regen} HP ({self.hp}/{self.max_hp})")
            # --- 结束新增 ---

            # 恢复魔法值（但不超过最大值）
            if self.mp < self.max_mp:
                old_mp = self.mp
                # --- 修改：使用 points_to_regen ---
                self.mp = min(self.max_mp, self.mp + points_to_regen)
                # --- 结束修改 ---
                actual_mp_regen = self.mp - old_mp
                if actual_mp_regen > 0:
                    logger.debug(f"玩家魔法自动恢复: +{actual_mp_regen} MP ({self.mp}/{self.max_mp})")

            # 更新上次恢复时间 (只有在实际恢复了才更新)
            self.last_regen_time = current_time

    @property
    def refresh_tokens(self):
        """获取商店刷新符数量"""
        return getattr(self, "_refresh_tokens", 0)

    @refresh_tokens.setter
    def refresh_tokens(self, value):
        """设置商店刷新符数量"""
        if value < 0:
            value = 0
        self._refresh_tokens = value

    def add_refresh_token(self, amount=1):
        """添加商店刷新符

        Args:
            amount: 添加的数量，默认为1

        Returns:
            int: 添加后的总数量
        """
        if not hasattr(self, "_refresh_tokens"):
            self._refresh_tokens = 0

        self._refresh_tokens += amount
        logger.info(f"添加了 {amount} 个商店刷新符，当前共有 {self._refresh_tokens} 个")
        return self._refresh_tokens

    def to_dict(self):
        """将玩家数据转换为字典，用于保存"""
        try:
            data = {
                "character_class": self.character_class,
                "name": self.name,
                "gender": self.gender,
                "level": self._level,
                "exp": self._exp,
                "gold": self._gold,
                "yuanbao": self._yuanbao,
                "vip_level": self._vip_level,
                "vip_activated": self._vip_activated,
                "refresh_tokens": self._refresh_tokens,

                "hp": self._hp,
                "max_hp": self._max_hp,
                "mp": self._mp,
                "max_mp": self._max_mp,

                "attack": self._attack,
                "defense": self._defense,
                "attack_speed": self._attack_speed,
                "crit_rate": self._crit_rate,
                "base_accuracy": self._base_accuracy,
                "agility": self._agility,
                "luck": self._luck,
                "magic_defense": self._magic_defense,
                "magic_dodge": self._magic_dodge,
                "required_exp": self.required_exp,
                "equipment": self.equipment,
                "inventory": self.inventory,
                "storage": self.storage,  # 保存仓库数据
                "locked_items": list(self.locked_items),  # 将集合转换为列表保存

                # 技能数据
                "skills": self.skills,
                "disabled_skills": list(self.disabled_skills),
                "skill_slots": self.skill_slots,
                "skill_proficiencies": getattr(self, 'skill_proficiencies', {}),
                "summons": [],
                "skill_cooldowns": getattr(self, 'skill_cooldowns', {}),
                "charmed_monsters": [],

                # 地下城挑战数据
                "dungeon_challenges": getattr(self, 'dungeon_challenges', 1),
                "last_dungeon_reset": getattr(self, 'last_dungeon_reset', time.time()),

                # --- 添加自动吃药设置 ---
                "auto_potion_enabled": getattr(self, 'auto_potion_enabled', False),
                "hp_potion_threshold": getattr(self, 'hp_potion_threshold', 50),
                "mp_potion_threshold": getattr(self, 'mp_potion_threshold', 30),
                "selected_hp_potions": getattr(self, 'selected_hp_potions', []),
                "selected_mp_potions": getattr(self, 'selected_mp_potions', []),
                # ------------------------

                 # --- 添加自动出售设置 ---
                "auto_sell_enabled": getattr(self, 'auto_sell_enabled', False),
                "sellable_qualities": getattr(self, 'sellable_qualities', {"普通": True, "精良": False, "稀有": False, "史诗": False, "传说": False})
                # ------------------------
            }

            return data
        except Exception as e:
            logger.error(f"保存玩家数据时出错: {e}")
            return {}

    def load_from_dict(self, data):
        """从字典加载玩家数据"""
        try:
            # 加载基本属性
            self.character_class = data.get("character_class", "战士")
            self.name = data.get("name", "")
            self.gender = data.get("gender", "男")

            # 确保有summons属性
            if not hasattr(self, 'summons'):
                self.summons = []

            # 确保有charmed_monsters属性
            if not hasattr(self, 'charmed_monsters'):
                self.charmed_monsters = []

            # 加载等级和经验
            self._level = data.get("level", 1)
            self._exp = data.get("exp", 0)

            # 加载货币
            self._gold = data.get("gold", 0)
            self._yuanbao = data.get("yuanbao", 5)

            # 加载VIP数据
            self._vip_level = data.get("vip_level", 0)
            self._vip_activated = data.get("vip_activated", [])
            self._refresh_tokens = data.get("refresh_tokens", 0)

            # 加载生命值和魔法值
            self._hp = data.get("hp", 0)
            self._max_hp = data.get("max_hp", 0)
            self._mp = data.get("mp", 0)
            self._max_mp = data.get("max_mp", 0)

            # 加载战斗属性
            self._attack = int(data.get("attack", 1))
            self._defense = int(data.get("defense", 0))
            self._attack_speed = float(data.get("attack_speed", 1.0))
            self._crit_rate = int(data.get("crit_rate", 0))
            self._base_accuracy = int(data.get("base_accuracy", 17))
            self._agility = int(data.get("agility", 15))
            self._luck = int(data.get("luck", 1))
            self._magic_defense = int(data.get("magic_defense", 0))
            self._magic_dodge = int(data.get("magic_dodge", 0))

            # 加载装备数据
            self.equipment = data.get("equipment", {}) # 使用 get 提供默认空字典
            if not self.equipment: # 如果加载后为空，则添加初始装备
                self.add_initial_equipment()

            # 加载背包数据
            self.inventory = data.get("inventory", [])
            self.storage = data.get("storage", [])
            self.locked_items = set(data.get("locked_items", []))

            # 加载技能数据
            self.skills = data.get("skills", {})
            self.disabled_skills = set(data.get("disabled_skills", []))

            # 加载技能槽位，注意键可能是字符串，需要转换回整数
            loaded_slots = data.get("skill_slots", {})
            self.skill_slots = {}
            if isinstance(loaded_slots, dict):
                 for k, v in loaded_slots.items():
                    try:
                        # 特殊处理 "initial" 键
                        if k == "initial":
                            self.skill_slots["initial"] = v
                        else:
                            self.skill_slots[int(k)] = v
                    except ValueError:
                        logger.warning(f"加载技能槽位时发现无效的键: {k}")
            else:
                logger.warning(f"加载的技能槽位数据格式不正确: {loaded_slots}")

            # 加载技能熟练度数据
            self.skill_proficiencies = data.get("skill_proficiencies", {})
            if not isinstance(self.skill_proficiencies, dict):
                logger.warning("加载的技能熟练度数据格式不正确，重置为空字典")
                self.skill_proficiencies = {}

            # 加载地下城挑战数据
            self.dungeon_challenges = data.get("dungeon_challenges", 1)
            self.last_dungeon_reset = data.get("last_dungeon_reset", time.time())

            # --- 添加加载自动吃药设置 ---
            self.auto_potion_enabled = data.get("auto_potion_enabled", False)
            self.hp_potion_threshold = data.get("hp_potion_threshold", 50)
            self.mp_potion_threshold = data.get("mp_potion_threshold", 30)
            self.selected_hp_potions = data.get("selected_hp_potions", [])
            self.selected_mp_potions = data.get("selected_mp_potions", [])
            # ------------------------

             # --- 添加加载自动出售设置 ---
            self.auto_sell_enabled = data.get("auto_sell_enabled", False)
            default_sell_qualities = {"普通": True, "精良": False, "稀有": False, "史诗": False, "传说": False}
            self.sellable_qualities = data.get("sellable_qualities", default_sell_qualities)
            # 确保加载的数据是字典
            if not isinstance(self.sellable_qualities, dict):
                logger.warning("加载的自动出售品质数据格式不正确，使用默认值")
                self.sellable_qualities = default_sell_qualities
            # ------------------------

            # 重新计算
            if self._attack_speed > 0: self.attack_interval = 1.0 / self._attack_speed
            self.calculate_equipment_bonus()
            if hasattr(self, '_cached_defense'): delattr(self, '_cached_defense')
            if hasattr(self, '_defense_cache_time'): delattr(self, '_defense_cache_time')
            if hasattr(self, '_cached_accuracy'): delattr(self, '_cached_accuracy')
            if hasattr(self, '_accuracy_cache_time'): delattr(self, '_accuracy_cache_time')

            self.is_dead = False
            logger.info(f"成功加载名为 {self.name} 的{self.character_class}角色数据")

        except Exception as e:
            logger.error(f"加载玩家数据失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def can_learn_skill(self, skill_id):
        """检查是否可以学习指定技能

        参数:
            skill_id: 技能ID

        返回:
            bool: 是否可以学习该技能
        """
        # 获取技能配置
        skill_config = GameConfig.get_skill(skill_id, self.character_class)
        if not skill_config:
            logger.warning(f"技能不存在或不属于该职业: {skill_id}, {self.character_class}")
            return False

        # 检查技能是否已达到最高等级
        current_level = self.skills.get(skill_id, 0)
        if current_level >= skill_config["max_level"]:
            logger.info(f"技能已达到最高等级: {skill_id}, 当前等级: {current_level}")
            return False

        # 检查玩家等级是否满足学习要求
        next_level = current_level + 1
        level_requirements = skill_config["level_requirements"]

        if next_level <= len(level_requirements):
            required_level = level_requirements[next_level - 1]
            if self.level < required_level:
                logger.info(f"玩家等级不足，无法学习技能: {skill_id}, 需要等级: {required_level}, 当前等级: {self.level}")
                return False
        else:
            logger.warning(f"技能配置错误: {skill_id}, level_requirements数组长度不足")
            return False

        # 检查熟练度要求（仅当技能等级大于0时才检查）
        if current_level > 0:
            points_required_list = skill_config.get("points_required")
            if points_required_list and isinstance(points_required_list, list) and current_level < len(points_required_list):
                required_proficiency = points_required_list[current_level]
                current_proficiency = self.skill_proficiencies.get(skill_id, 0)

                if current_proficiency < required_proficiency:
                    logger.info(f"技能熟练度不足，无法升级技能: {skill_id}, 需要熟练度: {required_proficiency}, 当前熟练度: {current_proficiency}")
                    return False

        return True

    def learn_skill(self, skill_id):
        """学习或升级技能

        参数:
            skill_id: 技能ID

        返回:
            bool: 是否成功学习
        """
        if not self.can_learn_skill(skill_id):
            return False

        # 获取当前技能等级
        current_level = self.skills.get(skill_id, 0)
        target_level = current_level + 1

        # 获取技能配置
        skill_config = GameConfig.get_skill(skill_id, self.character_class)
        skill_name = skill_config["name"] if skill_config else skill_id

        # 第一次学习技能时需要一本技能书
        if current_level == 0:
            # 获取技能书名称
            book_name = None
            if skill_config and "book_name" in skill_config:
                book_name = skill_config["book_name"]
            else:
                # 默认技能书命名规则
                book_name = f"{skill_name}"

            # 检查背包中是否有技能书
            found_book_idx = -1
            for idx, item in enumerate(self.inventory):
                if isinstance(item, dict) and item.get("name") == book_name:
                    found_book_idx = idx
                    break

            if found_book_idx == -1:
                logger.warning(f"学习技能需要 1 本{book_name}，背包中没有找到")
                if self.game:
                    self.game.add_log(f"学习技能需要 1 本{book_name}，背包中没有找到")
                return False

            # 从背包中移除技能书
            self.inventory.pop(found_book_idx)

            logger.info(f"消耗 1 本{book_name}学习了技能 {skill_name}")
            if self.game:
                self.game.add_log(f"消耗 1 本{book_name}学习了技能 {skill_name}")

        # 升级技能
        self.skills[skill_id] = target_level
        logger.info(f"玩家({self.name})学习了技能: {skill_name}, 当前等级: {self.skills[skill_id]}")

        # 如果是主动技能且是第一次学习，自动添加到技能槽
        if current_level == 0 and skill_config and skill_config.get("type") == "active":
            # 初始化技能槽（如果不存在）
            if not hasattr(self, 'skill_slots'):
                self.skill_slots = {}

            # 查找空闲的技能槽
            for slot_idx in range(6):  # 最多6个技能槽
                if slot_idx not in self.skill_slots:
                    # 将技能添加到空闲槽位
                    self.skill_slots[slot_idx] = skill_id
                    logger.info(f"将新学习的技能 {skill_name} 自动添加到技能槽 {slot_idx+1}")
                    if self.game:
                        self.game.add_log(f"将技能 {skill_name} 添加到技能槽 {slot_idx+1}")
                    break
            else:
                # 如果没有空闲槽位，记录日志
                logger.info(f"无法将技能 {skill_name} 添加到技能槽，所有槽位已满")

        return True

    def use_skill(self, skill_id, target=None):
        """使用技能

        参数:
            skill_id: 技能ID
            target: 技能目标

        返回:
            bool: 是否成功使用技能
        """
        # 检查技能是否已学习
        if skill_id not in self.skills or self.skills[skill_id] <= 0:
            logger.warning(f"尝试使用未学习的技能: {skill_id}")
            return False

        # 检查技能是否被禁用
        if skill_id in self.disabled_skills:
            logger.warning(f"尝试使用已禁用的技能: {skill_id}")
            return False

        # 获取技能配置
        skill_config = GameConfig.get_skill(skill_id, self.character_class)
        if not skill_config:
            logger.warning(f"技能不存在: {skill_id}")
            return False

        # 获取技能名称 - 提前获取，避免后面未定义错误
        skill_name = skill_config.get("name", skill_id)

        # 检查技能类型
        if skill_config["type"] == "passive":
            logger.warning(f"尝试主动使用被动技能: {skill_id}")
            return False

        # 检查全局技能冷却
        current_time = time.time()
        if current_time < self.global_skill_cooldown:
            remaining = self.global_skill_cooldown - current_time
            logger.info(f"全局技能冷却中，无法使用技能: {skill_id}, 剩余: {remaining:.1f}秒")
            return False

        # 检查技能冷却
        if skill_id in self.skill_cooldowns and current_time < self.skill_cooldowns[skill_id]:
            remaining = self.skill_cooldowns[skill_id] - current_time
            logger.info(f"技能仍在冷却中: {skill_id}, 剩余: {remaining:.1f}秒")
            return False

        # 检查MP是否足够
        mp_cost = skill_config.get("mp_cost", 0)
        if self.mp < mp_cost:
            logger.info(f"MP不足，无法使用技能: {skill_id}, 需要: {mp_cost}, 当前: {self.mp}")
            return False

        # 消耗MP
        self.mp -= mp_cost

        # 设置技能冷却
        cooldown = skill_config.get("cooldown", 0)
        if isinstance(cooldown, list):
            skill_level = self.skills[skill_id]
            if skill_level <= len(cooldown):
                cooldown = cooldown[skill_level - 1]
            else:
                cooldown = cooldown[-1]

        if cooldown > 0:
            self.skill_cooldowns[skill_id] = current_time + cooldown

        # 设置全局技能冷却（基于攻击间隔）
        self.global_skill_cooldown_duration = self.attack_interval
        self.global_skill_cooldown = current_time + self.global_skill_cooldown_duration

        # 增加技能熟练度
        if skill_config["type"] == "active":
            proficiency_gain = random.randint(2, 3)
            self.skill_proficiencies[skill_id] = self.skill_proficiencies.get(skill_id, 0) + proficiency_gain
            logger.info(f"玩家 {self.name} 使用技能 {skill_name} 获得 {proficiency_gain} 点熟练度，当前: {self.skill_proficiencies[skill_id]}")
            # 如果游戏实例存在，增加日志
            if self.game:
                self.game.add_log(f"技能熟练度+{proficiency_gain} ({self.skill_proficiencies[skill_id]})")

        # 应用技能效果
        skill_level = self.skills[skill_id]
        effect_index = min(skill_level - 1, len(skill_config["effects"]) - 1)
        effect = skill_config["effects"][effect_index]

        # 记录技能使用日志 (使用前面获取的skill_name)
        logger.info(f"玩家({self.name})使用了技能: {skill_name}, 等级: {skill_level}")

        # 根据效果类型分别处理
        effect_type = effect["type"]
        effect_value = effect["value"]

        # 获取额外属性（如持续时间）
        duration = effect.get("duration", 0)

        # 判断技能是否为魔法类技能
        is_magic_skill = False
        if effect_type in ["ranged_damage", "ground_damage", "charm", "heal", "poison", "damage_reduction"] or self.character_class in ["法师", "道士"]:
            is_magic_skill = True

        # 如果是游戏实例中使用技能，添加战斗日志
        if self.game and hasattr(self.game, 'add_log'):
            self.game.add_log(f"使用了技能 [{skill_name}]", True)

        # 获取魔法/道术加成比例
        magic_ratio = effect.get("magic_ratio", 0)
        taoism_ratio = effect.get("taoism_ratio", 0)

        # 处理召唤类技能
        if effect_type == "summon":
            summon_type = effect_value  # 召唤物类型，如"skeleton"或"beast"
            duration = effect.get("duration", -1)  # 持续时间，默认-1表示永久

            # 返回额外的召唤物信息
            return {
                "skill_id": skill_id,
                "skill_name": skill_name,
                "level": skill_level,
                "effect_type": effect_type,
                "effect_value": summon_type,
                "duration": duration,
                "target": target,
                "is_magic_skill": is_magic_skill,
                "magic_ratio": magic_ratio,
                "taoism_ratio": taoism_ratio
            }

        # 返回技能效果信息
        return {
            "skill_id": skill_id,
            "skill_name": skill_name,
            "level": skill_level,
            "effect_type": effect_type,
            "effect_value": effect_value,
            "duration": duration,
            "target": target,
            "is_magic_skill": is_magic_skill,
            "magic_ratio": magic_ratio,
            "taoism_ratio": taoism_ratio
        }

    def get_passive_skill_effects(self):
        """获取所有被动技能效果

        返回:
            dict: 被动技能效果
        """
        effects = {
            "accuracy": 0,
            "damage_percent": 0,
            "extra_damage": 0
        }

        # 遍历所有已学习的技能
        for skill_id, level in self.skills.items():
            if level <= 0:
                continue

            # 跳过已禁用的技能
            if skill_id in self.disabled_skills:
                continue

            # 获取技能配置
            skill_config = GameConfig.get_skill(skill_id, self.character_class)
            if not skill_config or skill_config["type"] != "passive":
                continue

            # 获取技能效果
            effect_index = min(level - 1, len(skill_config["effects"]) - 1)
            effect = skill_config["effects"][effect_index]

            # 根据效果类型应用效果
            effect_type = effect["type"]
            effect_value = effect["value"]

            if effect_type in effects:
                effects[effect_type] += effect_value

        return effects

    def get_skill_cooldown_info(self):
        """获取技能冷却信息

        返回:
            dict: 技能冷却信息 {skill_id: remaining_cooldown}
        """
        current_time = time.time()
        cooldown_info = {}

        for skill_id, end_time in self.skill_cooldowns.items():
            remaining = end_time - current_time
            if remaining > 0:
                cooldown_info[skill_id] = remaining
            else:
                cooldown_info[skill_id] = 0

        return cooldown_info

    def get_global_cooldown_info(self):
        """获取全局技能冷却信息

        返回:
            dict: 全局冷却信息 {
                'remaining': 剩余冷却时间,
                'duration': 总冷却时间,
                'progress': 冷却进度(0-1)
            }
        """
        current_time = time.time()
        remaining = max(0, self.global_skill_cooldown - current_time)

        # 如果冷却时间为0，返回完成状态
        if self.global_skill_cooldown_duration <= 0:
            return {
                'remaining': 0,
                'duration': 0,
                'progress': 1.0  # 完全冷却完毕
            }

        # 计算冷却进度 (0表示刚开始冷却，1表示冷却完毕)
        progress = 1.0 - (remaining / self.global_skill_cooldown_duration)
        progress = max(0, min(1.0, progress))  # 确保在0-1范围内

        return {
            'remaining': remaining,
            'duration': self.global_skill_cooldown_duration,
            'progress': progress
        }

    def update_cooldowns(self):
        """更新技能冷却状态"""
        current_time = time.time()
        expired_skills = []

        for skill_id, end_time in self.skill_cooldowns.items():
            if current_time >= end_time:
                expired_skills.append(skill_id)

        # 移除已过期的冷却
        for skill_id in expired_skills:
            del self.skill_cooldowns[skill_id]

        # 更新全局技能冷却
        if current_time >= self.global_skill_cooldown:
            self.global_skill_cooldown = 0

    def toggle_item_lock(self, inventory_index):
        """锁定或解锁背包中的物品

        Args:
            inventory_index: 背包中物品的索引

        Returns:
            bool: 操作是否成功，True表示成功切换锁定状态
        """
        try:
            # 检查索引是否有效
            if not isinstance(inventory_index, int) or inventory_index < 0 or inventory_index >= len(self.inventory):
                logger.warning(f"无效的物品索引: {inventory_index}, 背包大小: {len(self.inventory)}")
                return False

            # 获取物品
            item = self.inventory[inventory_index]
            if not item or not isinstance(item, dict):
                logger.warning(f"物品索引 {inventory_index} 处的物品无效: {item}")
                return False

            # 检查是否已锁定
            if inventory_index in self.locked_items:
                # 解锁物品
                self.locked_items.remove(inventory_index)
                logger.info(f"解锁物品: [{inventory_index}] {item.get('name', '未知物品')}")
                return True
            else:
                # 锁定物品
                self.locked_items.add(inventory_index)
                logger.info(f"锁定物品: [{inventory_index}] {item.get('name', '未知物品')}")
                return True
        except Exception as e:
            logger.error(f"切换物品锁定状态时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def is_item_locked(self, inventory_index):
        """检查指定物品是否被锁定"""
        if not isinstance(inventory_index, int) or inventory_index < 0:
            return False
        return inventory_index in self.locked_items

    def add_item(self, item, amount=1):
        """向玩家背包添加物品

        Args:
            item: 要添加的物品数据
            amount: 添加的数量

        Returns:
            bool: 是否成功添加或自动出售
        """
        try:
            # --- 修改：自动出售逻辑 ---
            if getattr(self, 'auto_sell_enabled', False) and isinstance(item, dict):
                # 明确定义可出售的装备类型
                valid_equipment_types = ["武器", "头盔", "防具", "手镯", "戒指", "项链", "勋章"]
                item_type = item.get("type", "其他") # 获取物品类型，默认为"其他"

                # 检查物品是否为可出售的装备类型
                if item_type in valid_equipment_types:
                    item_quality = item.get("quality", item.get("tier", "普通")) # 兼容 tier
                    sellable_qualities = getattr(self, 'sellable_qualities', {})

                    # 检查品质是否在自动出售列表中且设置为True
                    if sellable_qualities.get(item_quality, False):
                        # 计算售价 (调用计算售价的方法)
                        base_price = self.calculate_sell_price(item)

                        # 获取VIP加成
                        vip_bonus = 1.0 # 默认无加成
                        if hasattr(self, 'get_vip_sell_bonus'):
                            vip_bonus = self.get_vip_sell_bonus()

                        # 应用VIP加成计算最终价格
                        final_price = int(base_price * vip_bonus)

                        # 添加金币
                        self.gold += final_price

                        # 构建日志信息
                        # equipment_types 已被 valid_equipment_types 取代，这里直接使用 item_type
                        vip_info = f" (VIP{getattr(self,'vip_level',0)}+{int((vip_bonus-1)*100)}%)" if vip_bonus > 1.0 else ""
                        log_message = f"[自动出售] {item.get('name', '未知物品')} ({item_quality}, {item_type})，获得 {final_price} 金币{vip_info}"
                        logger.info(log_message)
                        if self.game: # 添加游戏内日志
                            self.game.add_log(log_message)

                        return True # 物品被自动出售，没有添加到背包
            # --- 结束自动出售逻辑 ---

            # 确保物品合法
            if not item:
                logger.error("尝试添加空物品")
                return False

            # 确保背包已初始化
            if not hasattr(self, "inventory"):
                self.inventory = []

            # 复制物品数据，避免引用原始对象导致的问题
            if isinstance(item, dict):
                item_copy = item.copy()
            else:
                # 如果不是字典，尝试转换
                item_copy = {"name": str(item), "type": "其他"}

            # 检查物品类型，如果是消耗品类型，尝试叠加
            item_type = item_copy.get("type", "")
            item_name = item_copy.get("name", "")

            # 消耗品类型列表，这些类型的物品可以堆叠
            consumable_types = ["消耗品", "恢复消耗品", "药剂", "药水", "任务道具"]

            if item_type in consumable_types:
                # 查找背包中是否已有相同名称的消耗品
                for existing_item in self.inventory:
                    if (isinstance(existing_item, dict) and
                        existing_item.get("name", "") == item_name and
                        existing_item.get("type", "") in consumable_types):

                        # 获取当前数量，如果没有数量字段则默认为1
                        current_amount = existing_item.get("amount", 1)
                        # 增加数量
                        existing_item["amount"] = current_amount + amount
                        logger.info(f"物品叠加：{item_name} 数量增加 {amount}，当前数量：{existing_item['amount']}")
                        return True

            # 如果不是消耗品或者背包中没有找到相同的物品，添加为新物品
            # 添加数量信息（对所有物品都添加，方便处理）
            if amount > 1 or item_type in consumable_types:
                item_copy["amount"] = amount

            # 检查背包是否已满
            if len(self.inventory) >= self.max_inventory_slots:
                logger.warning("背包已满，无法添加更多物品")
                return False

            # 添加到背包
            self.inventory.append(item_copy)
            logger.info(f"已将物品 {item_copy.get('name', '未知物品')} 添加到背包{', 数量: ' + str(amount) if amount > 1 else ''}")
            return True
        except Exception as e:
            logger.error(f"添加物品到背包时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def add_equipment(self, equipment):
        """添加装备到玩家背包

        Args:
            equipment: 装备数据

        Returns:
            bool: 是否成功添加
        """
        # 直接调用add_item方法
        return self.add_item(equipment)

    def toggle_skill_disabled(self, skill_id):
        """切换技能的启用/禁用状态

        参数:
            skill_id: 技能ID

        返回:
            bool: 操作后的禁用状态，True表示禁用，False表示启用
        """
        if skill_id not in self.skills or self.skills[skill_id] <= 0:
            logger.warning(f"尝试切换未学习技能的启用状态: {skill_id}")
            return False

        if skill_id in self.disabled_skills:
            # 启用技能
            self.disabled_skills.remove(skill_id)
            logger.info(f"启用技能: {skill_id}")
            return False
        else:
            # 禁用技能
            self.disabled_skills.add(skill_id)
            logger.info(f"禁用技能: {skill_id}")
            return True

    def is_skill_disabled(self, skill_id):
        """检查技能是否被禁用

        参数:
            skill_id: 技能ID

        返回:
            bool: 是否被禁用，True表示禁用，False表示启用
        """
        return skill_id in self.disabled_skills

    @property
    def magic_attack(self):
        """获取玩家的魔法攻击范围，直接基于装备的魔法加成"""
        # 获取装备加成
        equipment_bonus = self.get_equipment_bonus()

        # 如果有魔法加成，直接使用它
        if equipment_bonus and "magic" in equipment_bonus:
            magic_min, magic_max = equipment_bonus["magic"]
            return magic_min, magic_max

        # 如果没有魔法加成，返回0
        return 0, 0

    def calculate_magic_damage(self, target=None):
        """计算魔法伤害

        Args:
            target: 可选的目标对象，用于计算针对特定目标的伤害

        Returns:
            tuple: (伤害值, 是否暴击)
        """
        try:
            import random
            min_magic, max_magic = self.magic_attack

            # 获取玩家幸运值
            player_luck = self.luck

            # 根据幸运值确定最大伤害的概率
            max_damage_probabilities = {
                0: 0.1,     # 10%
                1: 1/9,     # 11.1%
                2: 1/8,     # 12.5%
                3: 1/7,     # 14.3%
                4: 1/6,     # 16.7%
                5: 0.2,     # 20%
                6: 0.25,    # 25%
                7: 1/3,     # 33%
                8: 0.5,     # 50%
            }

            # 如果幸运值≥9，必定造成最大伤害
            if player_luck >= 9:
                damage = max_magic
                logger.debug(f"魔法攻击：幸运值{player_luck}≥9，造成最大伤害: {damage}")
            else:
                # 获取对应幸运值的最大伤害概率
                max_damage_prob = max_damage_probabilities.get(player_luck, 0.1)  # 默认为10%

                # 随机决定是否造成最大伤害
                if random.random() < max_damage_prob:
                    damage = max_magic
                    logger.debug(f"魔法攻击：幸运值{player_luck}触发最大伤害({max_damage_prob*100:.1f}%概率)")
                else:
                    # 如果不是最大伤害，在最小值和最大值之间随机
                    damage = random.randint(min_magic, max_magic)
                    logger.debug(f"魔法攻击：幸运值{player_luck}下的普通伤害: {damage} (范围: {min_magic}-{max_magic})")

            # 应用暴击
            equipment_bonus = self.get_equipment_bonus()
            crit_rate = equipment_bonus.get("crit_rate", 0)

            # 暴击判定
            rand_value = random.random()
            is_crit = rand_value < crit_rate

            # 暴击加成
            if is_crit:
                damage = int(damage * 1.5)  # 暴击增加50%伤害

            logger.debug(f"计算魔法伤害: 基础={min_magic}-{max_magic}, 最终={damage}, 暴击={is_crit}, 幸运值={player_luck}")
            return (damage, is_crit)
        except Exception as e:
            logger.error(f"计算魔法伤害时出错: {e}")
            return (1, False)  # 出错时返回最小伤害

    @property
    def taoism_attack(self):
        """获取玩家的道术攻击范围，直接基于装备的道术加成"""
        # 获取装备加成
        equipment_bonus = self.get_equipment_bonus()

        # 如果有道术加成，直接使用它
        if equipment_bonus and "taoism" in equipment_bonus:
            taoism_min, taoism_max = equipment_bonus["taoism"]
            return taoism_min, taoism_max

        # 如果没有道术加成，返回0
        return 0, 0

    def calculate_taoism_damage(self, target=None):
        """计算道术伤害

        Args:
            target: 可选的目标对象，用于计算针对特定目标的伤害

        Returns:
            tuple: (伤害值, 是否暴击)
        """
        try:
            import random
            min_taoism, max_taoism = self.taoism_attack

            # 获取玩家幸运值
            player_luck = self.luck

            # 根据幸运值确定最大伤害的概率
            max_damage_probabilities = {
                0: 0.1,     # 10%
                1: 1/9,     # 11.1%
                2: 1/8,     # 12.5%
                3: 1/7,     # 14.3%
                4: 1/6,     # 16.7%
                5: 0.2,     # 20%
                6: 0.25,    # 25%
                7: 1/3,     # 33%
                8: 0.5,     # 50%
            }

            # 如果幸运值≥9，必定造成最大伤害
            if player_luck >= 9:
                damage = max_taoism
                logger.debug(f"道术攻击：幸运值{player_luck}≥9，造成最大伤害: {damage}")
            else:
                # 获取对应幸运值的最大伤害概率
                max_damage_prob = max_damage_probabilities.get(player_luck, 0.1)  # 默认为10%

                # 随机决定是否造成最大伤害
                if random.random() < max_damage_prob:
                    damage = max_taoism
                    logger.debug(f"道术攻击：幸运值{player_luck}触发最大伤害({max_damage_prob*100:.1f}%概率)")
                else:
                    # 如果不是最大伤害，在最小值和最大值之间随机
                    damage = random.randint(min_taoism, max_taoism)
                    logger.debug(f"道术攻击：幸运值{player_luck}下的普通伤害: {damage} (范围: {min_taoism}-{max_taoism})")

            # 应用暴击
            crit_chance = self.crit_rate / 100.0  # 使用装备提供的暴击率
            is_crit = random.random() < crit_chance
            if is_crit:
                damage = int(damage * 1.5)  # 暴击伤害1.5倍

            logger.debug(f"计算道术伤害: 基础={min_taoism}-{max_taoism}, 最终={damage}, 暴击={is_crit}, 幸运值={player_luck}")
            return (damage, is_crit)
        except Exception as e:
            logger.error(f"计算道术伤害时出错: {e}")
            return (1, False)  # 出错时返回最小伤害

    @property
    def taoism(self):
        """获取玩家的道术值（基础值+装备加成）"""
        # 获取装备加成
        equipment_bonus = self.get_equipment_bonus()
        equipment_taoism = 0
        if "taoism" in equipment_bonus:
            # 使用道术范围的平均值作为道术属性值
            taoism_min, taoism_max = equipment_bonus["taoism"]
            equipment_taoism = taoism_max

        # 返回基础道术加装备加成
        return equipment_taoism

    @property
    def magic(self):
        """获取玩家的魔法值（基础值+装备加成）"""
        # 获取装备加成
        equipment_bonus = self.get_equipment_bonus()
        equipment_magic = 0
        if "magic" in equipment_bonus:
            # 使用魔法范围的最大值作为魔法属性值
            magic_min, magic_max = equipment_bonus["magic"]
            equipment_magic = magic_max

        # 返回基础魔法加装备加成
        return equipment_magic

    def update(self, current_time, dt):
        """更新玩家状态

        参数:
            current_time: 当前时间戳
            dt: 时间增量(秒)
        """
        # 生命值和魔法值自动恢复
        self.update_regeneration(current_time)

        # 更新技能冷却时间
        self.update_cooldowns()

        # 更新被动技能熟练度
        self.update_passive_skill_proficiency(dt)

    def update_passive_skill_proficiency(self, dt):
        """更新被动技能熟练度

        参数:
            dt: 时间增量(秒)
        """
        # 累积时间，每30秒检查一次
        if not hasattr(self, '_passive_proficiency_timer'):
            self._passive_proficiency_timer = 0

        self._passive_proficiency_timer += dt
        if self._passive_proficiency_timer < 30:
            return

        # 重置计时器
        self._passive_proficiency_timer = 0

        # 遍历所有已学习的技能
        for skill_id, level in self.skills.items():
            # 跳过未学习或已禁用的技能
            if level <= 0 or skill_id in self.disabled_skills:
                continue

            # 获取技能配置
            skill_config = GameConfig.get_skill(skill_id, self.character_class)
            if not skill_config:
                continue

            # 仅处理被动技能
            if skill_config["type"] != "passive":
                continue

            # 检查技能是否有熟练度需求
            if not skill_config.get("points_required"):
                continue

            # 增加被动技能熟练度（每30秒增加1点）
            if not hasattr(self, 'skill_proficiencies'):
                self.skill_proficiencies = {}

            current_prof = self.skill_proficiencies.get(skill_id, 0)
            self.skill_proficiencies[skill_id] = current_prof + 1

            # 记录日志，但降低频率（避免日志过多）
            if current_prof % 5 == 0:  # 每增加5点记录一次
                logger.debug(f"被动技能 {skill_config.get('name', skill_id)} 熟练度增加，当前: {self.skill_proficiencies[skill_id]}")

    def gain_battle_proficiency(self, battle_time):
        """战斗结束后获得被动技能熟练度

        参数:
            battle_time: 战斗持续时间(秒)
        """
        # 战斗相关被动技能增加熟练度
        for skill_id, level in self.skills.items():
            # 跳过未学习或已禁用的技能
            if level <= 0 or skill_id in self.disabled_skills:
                continue

            # 获取技能配置
            skill_config = GameConfig.get_skill(skill_id, self.character_class)
            if not skill_config:
                continue

            # 仅处理被动技能
            if skill_config["type"] != "passive":
                continue

            # 检查技能是否有熟练度需求
            if not skill_config.get("points_required"):
                continue

            # 战斗相关被动技能每分钟战斗增加2-4点熟练度
            proficiency_gain = random.randint(2, 4) * (battle_time / 60)
            proficiency_gain = max(1, int(proficiency_gain))  # 至少增加1点

            # 初始化熟练度属性（如果不存在）
            if not hasattr(self, 'skill_proficiencies'):
                self.skill_proficiencies = {}

            # 增加熟练度
            self.skill_proficiencies[skill_id] = self.skill_proficiencies.get(skill_id, 0) + proficiency_gain

            # 记录日志（如果game存在）
            if self.game:
                self.game.add_log(f"被动技能熟练度+{proficiency_gain} ({self.skill_proficiencies[skill_id]})")

    # --- 新增：计算物品售价 ---
    def calculate_sell_price(self, item: Dict) -> int:
        """计算物品的基础售价 (不含VIP加成)

        Args:
            item: 物品字典

        Returns:
            int: 计算出的基础售价
        """
        if not isinstance(item, dict):
            return 50  # 默认最低价格

        # 基础价格为100
        base_price_value = 100

        # 根据装备属性增加价格，每点属性+100
        total_attributes = 0

        # 统计攻击属性
        if "attack" in item:
            if isinstance(item["attack"], list):
                total_attributes += item["attack"][1]
            else:
                total_attributes += item["attack"]

        # 统计防御属性
        if "defense" in item:
            if isinstance(item["defense"], list):
                total_attributes += item["defense"][1]
            else:
                total_attributes += item["defense"]

        # 统计魔法属性
        if "magic" in item:
            if isinstance(item["magic"], list):
                total_attributes += item["magic"][1]
            else:
                total_attributes += item["magic"]

        # 统计魔法防御属性
        if "magic_defense" in item:
            if isinstance(item["magic_defense"], list):
                total_attributes += item["magic_defense"][1]
            else:
                total_attributes += item["magic_defense"]

        # 统计道术属性
        if "taoism" in item:
            if isinstance(item["taoism"], list):
                total_attributes += item["taoism"][1]
            else:
                total_attributes += item["taoism"]

        # 其他可能的属性
        for attr in ["accuracy", "attack_speed", "agility"]:
            if attr in item:
                total_attributes += item[attr]

        # 处理特殊的幸运值属性
        if "luck" in item:
            if isinstance(item["luck"], str) and item["luck"] == "random":
                total_attributes += 1
                logger.debug(f"物品 {item.get('name', '未知')} 的随机幸运值按1点计算售价")
            else:
                 # 确保幸运值是数字
                try:
                    total_attributes += int(item["luck"])
                except (ValueError, TypeError):
                    logger.warning(f"物品 {item.get('name', '未知')} 的幸运值不是有效数字: {item['luck']}")


        # 处理魔法闪避属性
        if "magic_avoidance" in item or "magic_dodge" in item:
            magic_avoid = item.get("magic_avoidance", 0) + item.get("magic_dodge", 0)
            total_attributes += magic_avoid // 10
            logger.debug(f"物品 {item.get('name', '未知')} 的魔法闪避{magic_avoid}%按{magic_avoid // 10}点属性计算售价")

        # 计算物品价值：基础价格 + 属性总和 * 100
        item_value = base_price_value + total_attributes * 100

        # 品质加成
        item_quality = item.get("quality", item.get("tier", "普通")) # 兼容 tier
        quality_multiplier = {
            "普通": 1.0,
            "精良": 1.2,
            "稀有": 1.5,
            "史诗": 2.0,
            "传说": 3.0
        }.get(item_quality, 1.0)

        # 应用品质加成
        item_value = int(item_value * quality_multiplier)

        # 出售价格为原价的一半（按50%算），最低50
        sell_price = max(50, item_value // 2)

        return sell_price
    # --- 结束新增 ---

    # --- 自动吃药系统接口 ---

    def is_auto_potion_enabled(self) -> bool:
        """检查自动吃药功能是否启用"""
        return getattr(self, 'auto_potion_enabled', False)

    def get_hp_potion_threshold(self) -> Optional[int]:
        """获取玩家设置的HP药水使用阈值（百分比）

        Returns:
            Optional[int]: 阈值百分比，如果玩家未设置则返回 None
        """
        # 返回 None 表示玩家未设置，让调用者决定使用默认值
        return getattr(self, 'hp_potion_threshold', None)

    def get_mp_potion_threshold(self) -> Optional[int]:
        """获取玩家设置的MP药水使用阈值（百分比）

        Returns:
            Optional[int]: 阈值百分比，如果玩家未设置则返回 None
        """
        # 返回 None 表示玩家未设置
        return getattr(self, 'mp_potion_threshold', None)

    def get_selected_hp_potions(self) -> List[str]:
        """获取玩家选择的优先使用的HP药水列表"""
        return getattr(self, 'selected_hp_potions', [])

    def get_selected_mp_potions(self) -> List[str]:
        """获取玩家选择的优先使用的MP药水列表"""
        return getattr(self, 'selected_mp_potions', [])

    def get_selected_dual_potions(self) -> List[str]:
        """获取玩家选择的优先使用的双恢复药水列表

        注意：目前此设置可能未在UI或数据中实现，但提供接口以备将来使用。
        """
        return getattr(self, 'selected_dual_potions', [])

    # --- End Auto Potion Interface ---

    def revive(self):
        """复活玩家"""
        self.is_dead = False
        self.hp = max(1, int(self.max_hp * 0.1))  # 恢复 10% HP，至少 1 点
        logger.info(f"玩家 {self.name} 已复活，HP: {self.hp}/{self.max_hp}")
        return True