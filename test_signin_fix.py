#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
签到系统修复测试脚本
验证每个角色都有独立的签到记录
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signin_file_separation():
    """测试签到文件分离功能"""
    print("=" * 60)
    print("测试签到系统角色分离功能")
    print("=" * 60)
    
    # 模拟两个不同的角色名称
    character_names = ["测试角色1", "测试角色2"]
    
    # 清理可能存在的测试文件
    for name in character_names:
        safe_name = "".join(c for c in name if c.isalnum() or c in ('-', '_'))
        test_file = f"data/saves/signin_records_{safe_name}.json"
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"清理旧的测试文件: {test_file}")
    
    # 为每个角色创建独立的签到记录
    for i, character_name in enumerate(character_names):
        print(f"\n--- 测试角色: {character_name} ---")
        
        # 生成安全的文件名
        safe_name = "".join(c for c in character_name if c.isalnum() or c in ('-', '_'))
        signin_file = f"data/saves/signin_records_{safe_name}.json"
        
        print(f"签到文件路径: {signin_file}")
        
        # 创建不同的签到记录
        signin_data = {
            "last_signin_time": time.time() if i == 0 else 0,  # 第一个角色已签到，第二个未签到
            "signin_days": i + 1,  # 不同的连续签到天数
            "total_signin_days": (i + 1) * 5  # 不同的总签到天数
        }
        
        # 确保目录存在
        os.makedirs(os.path.dirname(signin_file), exist_ok=True)
        
        # 保存签到记录
        with open(signin_file, 'w', encoding='utf-8') as f:
            json.dump(signin_data, f, indent=2)
        
        print(f"创建签到记录: {signin_data}")
        
        # 验证文件是否正确创建
        if os.path.exists(signin_file):
            with open(signin_file, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
                print(f"验证加载数据: {loaded_data}")
                
                if loaded_data == signin_data:
                    print("✅ 签到记录创建和加载成功")
                else:
                    print("❌ 签到记录数据不匹配")
        else:
            print("❌ 签到记录文件创建失败")
    
    print(f"\n--- 验证文件分离 ---")
    
    # 验证两个角色的签到文件是否独立
    files_created = []
    for character_name in character_names:
        safe_name = "".join(c for c in character_name if c.isalnum() or c in ('-', '_'))
        signin_file = f"data/saves/signin_records_{safe_name}.json"
        
        if os.path.exists(signin_file):
            files_created.append(signin_file)
            
            # 读取并显示内容
            with open(signin_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"文件 {signin_file}:")
                print(f"  连续签到天数: {data['signin_days']}")
                print(f"  总签到天数: {data['total_signin_days']}")
                print(f"  最后签到时间: {datetime.fromtimestamp(data['last_signin_time']) if data['last_signin_time'] > 0 else '从未签到'}")
    
    if len(files_created) == len(character_names):
        print(f"✅ 成功创建 {len(files_created)} 个独立的签到文件")
        
        # 检查文件内容是否不同
        if len(files_created) >= 2:
            with open(files_created[0], 'r', encoding='utf-8') as f1:
                data1 = json.load(f1)
            with open(files_created[1], 'r', encoding='utf-8') as f2:
                data2 = json.load(f2)
            
            if data1 != data2:
                print("✅ 不同角色的签到记录确实不同")
                return True
            else:
                print("❌ 不同角色的签到记录相同，可能存在问题")
                return False
    else:
        print(f"❌ 只创建了 {len(files_created)} 个文件，期望 {len(character_names)} 个")
        return False

def test_character_name_safety():
    """测试角色名称安全处理"""
    print(f"\n--- 测试角色名称安全处理 ---")
    
    # 测试包含特殊字符的角色名称
    test_names = [
        "角色@#$%",
        "角色 with spaces",
        "角色/\\:*?\"<>|",
        "正常角色名"
    ]
    
    for name in test_names:
        safe_name = "".join(c for c in name if c.isalnum() or c in ('-', '_'))
        print(f"原始名称: '{name}' -> 安全名称: '{safe_name}'")
        
        # 验证安全名称可以用作文件名
        test_file = f"data/saves/signin_records_{safe_name}.json"
        try:
            # 尝试创建文件
            os.makedirs(os.path.dirname(test_file), exist_ok=True)
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump({"test": True}, f)
            
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
            
            print(f"  ✅ 安全名称可以用作文件名")
        except Exception as e:
            print(f"  ❌ 安全名称无法用作文件名: {e}")

def main():
    """主函数"""
    print("签到系统角色分离修复测试")
    print("=" * 60)
    
    # 测试签到文件分离
    success = test_signin_file_separation()
    
    # 测试角色名称安全处理
    test_character_name_safety()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 签到系统角色分离功能测试通过！")
        print("现在每个角色都有独立的签到记录，")
        print("一个角色签到后不会影响其他角色的签到状态。")
    else:
        print("❌ 签到系统角色分离功能测试失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
