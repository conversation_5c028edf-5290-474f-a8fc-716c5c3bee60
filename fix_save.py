#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
from pathlib import Path
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def fix_save_file(save_path, output_path=None):
    """
    修复存档文件中player字段为空的问题
    
    参数:
        save_path: 存档文件路径
        output_path: 输出文件路径，如果为None则创建备份并覆盖原文件
    
    返回:
        bool: 是否成功修复
    """
    try:
        # 读取存档文件
        logger.info(f"正在读取存档文件: {save_path}")
        with open(save_path, 'r', encoding='utf-8') as f:
            save_data = json.load(f)
        
        # 检查存档结构
        if not isinstance(save_data, dict):
            logger.error("存档格式错误: 不是有效的JSON对象")
            return False
        
        # 检查player字段
        if "player" not in save_data or not save_data["player"]:
            logger.warning("检测到player字段为空，尝试修复")
            
            # 创建基本玩家数据
            save_data["player"] = {
                "character_class": "战士",
                "name": "存档修复角色",
                "gender": "男",
                "level": 1,
                "exp": 0,
                "gold": 0,
                "yuanbao": 5,
                "vip_level": 0,
                "vip_activated": [],
                "refresh_tokens": 0,
                "hp": 100,
                "max_hp": 100,
                "mp": 50,
                "max_mp": 50,
                "attack": 10,
                "defense": 5,
                "attack_speed": 1.0,
                "crit_rate": 5,
                "base_accuracy": 17,
                "agility": 15,
                "luck": 1,
                "magic_defense": 0,
                "magic_dodge": 0,
                "required_exp": 100,
                "equipment": {},
                "inventory": [],
                "storage": [],
                "locked_items": [],
                "skills": {},
                "disabled_skills": [],
                "skill_slots": {},
                "skill_proficiencies": {}
            }
        
        # 确保version字段存在
        if "version" not in save_data:
            logger.warning("存档中缺少version字段，添加默认版本号1.3.2")
            save_data["version"] = "1.3.2"
        
        # 确保game_state字段存在且完整
        if "game_state" not in save_data:
            logger.warning("存档中缺少game_state字段，添加默认状态")
            save_data["game_state"] = {
                "in_battle": False,
                "battle_enabled": True,
                "auto_battle": False,
                "auto_save": False
            }
        else:
            # 确保game_state字段包含所有必要的子字段
            required_fields = ["in_battle", "battle_enabled", "auto_battle", "auto_save"]
            for field in required_fields:
                if field not in save_data["game_state"]:
                    default_value = True if field == "battle_enabled" else False
                    logger.warning(f"game_state中缺少{field}字段，添加默认值{default_value}")
                    save_data["game_state"][field] = default_value
        
        # 确保battle_stats字段存在且完整
        if "battle_stats" not in save_data:
            logger.warning("存档中缺少battle_stats字段，添加默认战斗统计")
            save_data["battle_stats"] = {
                "exp_gained": 0,
                "gold_gained": 0,
                "monsters_killed": 0,
                "battles_count": 0,
                "battle_logs": [],
                "equipment_drops": {
                    "普通": 0,
                    "精良": 0,
                    "稀有": 0,
                    "史诗": 0, 
                    "传说": 0
                },
                "auto_battle_time": 0,
                "battle_start_time": 0,
                "kills": 0,
                "total_exp": 0,
                "total_gold": 0
            }
        
        # 更新save_time和timestamp
        save_data["save_time"] = time.strftime("%Y-%m-%d %H:%M:%S")
        save_data["timestamp"] = time.time()
        
        # 确定输出路径
        if output_path is None:
            # 创建备份
            backup_path = f"{save_path}.bak.{int(time.time())}"
            try:
                import shutil
                shutil.copy2(save_path, backup_path)
                logger.info(f"创建存档备份: {backup_path}")
                output_path = save_path
            except Exception as e:
                logger.error(f"创建备份失败: {e}")
                output_path = f"{save_path}.fixed.json"
        
        # 写入修复后的存档
        logger.info(f"正在保存修复后的存档到: {output_path}")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        logger.info("存档修复完成")
        return True
        
    except Exception as e:
        logger.error(f"修复存档时发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python fix_save.py <存档文件路径> [输出文件路径]")
        return
    
    save_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(save_path):
        logger.error(f"存档文件不存在: {save_path}")
        return
    
    success = fix_save_file(save_path, output_path)
    if success:
        logger.info("存档修复成功！")
    else:
        logger.error("存档修复失败！")

if __name__ == "__main__":
    main() 