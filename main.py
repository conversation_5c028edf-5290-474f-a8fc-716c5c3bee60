#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import pygame
import logging
import traceback
import ctypes
import locale
import threading  # 用于系统托盘线程
import time  # 用于延时操作
from ctypes import wintypes
from typing import Optional
from utils.logger import setup_logger, logger
from utils.resource_manager import resources
from utils.utils import ConfirmDialog, MessageDialog
from core.game import Game
from ui.ui_manager import UIManager
from ui.screens.main_menu import MainMenu
from ui.screens.game_screen import GameScreen
from ui.screens.character_creation import CharacterCreation
from ui.screens.load_game import LoadGame
from ui.screens.settings import Settings
from ui.screens.map_screen import MapScreen
from ui.screens.skills_screen import SkillsScreen
from ui.screens.achievements_screen import AchievementsScreen
from ui.screens.inventory_screen import InventoryScreen
from ui.screens.equipment_screen import EquipmentScreen
from ui.screens.shop_screen import ShopScreen
from ui.screens.vip.vip_screen import VIPScreen
from ui.screens.signin.signin_screen import SignInScreen
from ui.screens.battle_stats_screen import BattleStatsScreen
from ui.screens.login_screen import LoginScreen
from ui.screens.character_creation_screen import CharacterCreationScreen

# 导入系统托盘相关库
try:
    # 添加详细的导入日志
    import sys
    import os

    # 打印Python环境信息
    logger.warning(f"Python版本: {sys.version}")
    logger.warning(f"Python可执行文件: {sys.executable}")
    logger.warning(f"当前工作目录: {os.getcwd()}")
    logger.warning(f"Python路径: {sys.path}")

    # 尝试查找pystray库
    try:
        import importlib.util
        pystray_spec = importlib.util.find_spec("pystray")
        if pystray_spec:
            logger.warning(f"找到pystray库: {pystray_spec.origin}")
        else:
            logger.warning("找不到pystray库")
    except Exception as find_error:
        logger.warning(f"查找pystray库时出错: {find_error}")

    # 尝试导入库
    import pystray  # type: ignore # 忽略 Pylance 的导入警告
    logger.warning("成功导入pystray库")

    from PIL import Image  # type: ignore
    logger.warning("成功导入PIL库")

    TRAY_AVAILABLE = True
    logger.warning("系统托盘功能可用")
except ImportError as e:
    logger.warning(f"未安装pystray或PIL库，系统托盘功能将不可用: {e}")
    # 尝试安装pystray库
    try:
        logger.warning("尝试自动安装pystray和pillow库...")
        import subprocess

        # 在Windows系统上，使用--user选项安装到用户目录
        if sys.platform == "win32":
            install_cmd = [sys.executable, "-m", "pip", "install", "--user", "pystray", "pillow"]

        logger.warning(f"执行安装命令: {' '.join(install_cmd)}")
        result = subprocess.run(install_cmd, capture_output=True, text=True)
        logger.warning(f"安装结果: {result.returncode}")
        logger.warning(f"安装输出: {result.stdout}")
        if result.stderr:
            logger.warning(f"安装错误: {result.stderr}")

        # 再次尝试导入
        try:
            import pystray
            from PIL import Image
            TRAY_AVAILABLE = True
            logger.warning("安装并导入成功，系统托盘功能可用")
        except ImportError as e2:
            logger.warning(f"安装后仍无法导入: {e2}")
            TRAY_AVAILABLE = False
    except Exception as install_error:
        logger.warning(f"自动安装失败: {install_error}")
        TRAY_AVAILABLE = False
# 副本系统已移除

# 设置 Python 的默认编码为 UTF-8
# 这对于中文显示和文件处理非常重要
if sys.platform.startswith('win'):
    # Windows 平台特殊处理
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 尝试设置控制台代码页为 UTF-8 (CP65001)
    try:
        os.system('chcp 65001 > nul')
    except Exception as e:
        logger.warning(f"设置控制台代码页失败: {e}")

# 检查当前编码
default_encoding = sys.getdefaultencoding()
locale_encoding = locale.getpreferredencoding()
print(f"系统编码: 默认={default_encoding}, 区域={locale_encoding}")

# 定义WINDOWPLACEMENT结构体
if sys.platform == "win32":
    class POINT(ctypes.Structure):
        _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]

    class RECT(ctypes.Structure):
        _fields_ = [("left", wintypes.LONG),
                   ("top", wintypes.LONG),
                   ("right", wintypes.LONG),
                   ("bottom", wintypes.LONG)]

    class WINDOWPLACEMENT(ctypes.Structure):
        _fields_ = [("length", ctypes.c_uint),
                   ("flags", ctypes.c_uint),
                   ("showCmd", ctypes.c_uint),
                   ("ptMinPosition", POINT),
                   ("ptMaxPosition", POINT),
                   ("rcNormalPosition", RECT)]

def resource_path(relative_path):
    """ 获取资源的绝对路径，处理打包和非打包环境"""
    try:
        # PyInstaller创建临时文件夹并将路径存储在_MEIPASS中
        if hasattr(sys, '_MEIPASS'):
            # 打包后的临时资源目录
            base_path = sys._MEIPASS
            logger.info(f"使用PyInstaller打包路径: {base_path}")
            return os.path.join(base_path, relative_path)

        # 开发环境中的资源路径
        base_path = os.path.abspath(".")
        logger.info(f"使用开发环境路径: {base_path}")
        return os.path.join(base_path, relative_path)
    except Exception as e:
        logger.error(f"资源路径解析错误: {e}", exc_info=True)
        # 如果出错，返回相对路径
        return relative_path

# 设置导入路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))



class GameApp:
    """游戏应用主类"""

    def __init__(self, screen_width: int = 1280, screen_height: int = 720, fullscreen: bool = False):
        """
        初始化游戏应用

        参数:
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            fullscreen: 是否全屏显示
        """
        # 初始化日志
        setup_logger(level=logging.WARNING)  # 恢复为WARNING级别
        logger.warning("游戏启动")

        # 初始化Pygame
        self._init_pygame()

        # 创建游戏窗口
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.fullscreen = fullscreen
        self._create_window()

        # 初始化资源管理器
        self._init_resources()

        # 创建游戏实例
        self.game = Game()

        # 创建默认玩家，以便能够使用自动战斗功能
        if not self.game.player:
            logger.info("创建默认玩家以便能够使用自动战斗功能")
            self.game.create_new_character("战士", "默认玩家", "男")

        # 创建UI管理器
        self.ui_manager = UIManager()
        self.ui_manager.initialize(self.game)

        # 创建界面
        self._create_screens()

        # 设置游戏时钟
        self.clock = pygame.time.Clock()
        self.fps = 60

        # 游戏状态
        self.running = True
        # 当前活动的对话框
        self.current_dialog: Optional[ConfirmDialog | MessageDialog] = None

        # 系统托盘相关
        self.tray_icon = None
        self.is_minimized_to_tray = False
        self.tray_thread = None

        # 初始化系统托盘
        if TRAY_AVAILABLE and sys.platform == "win32":
            self._init_tray_icon()

    def _init_pygame(self):
        """初始化Pygame库"""
        pygame.init()

        # 添加音频初始化异常处理
        try:
            pygame.mixer.init()
            self.audio_available = True
            logger.info("音频系统初始化成功")
        except Exception as e:
            self.audio_available = False
            logger.warning(f"音频系统初始化失败: {e}")
            logger.warning("游戏将在无声模式下运行")

        pygame.font.init()

        # 启用SDL2文本输入，支持中文等输入法
        os.environ['SDL_IME_SHOW_UI'] = '1'
        pygame.key.start_text_input()

        logger.info("Pygame初始化完成")

    def _create_window(self):
        """创建游戏窗口"""
        # 导入版本号
        from core.version import GAME_VERSION

        flags = pygame.FULLSCREEN if self.fullscreen else 0
        self.screen = pygame.display.set_mode((self.screen_width, self.screen_height), flags)
        pygame.display.set_caption(f"萝卜放置传奇 v{GAME_VERSION} - 交流QQ群：453817190")
        logger.info(f"创建游戏窗口: {self.screen_width}x{self.screen_height}, 全屏: {self.fullscreen}, 版本: {GAME_VERSION}")

    def _init_resources(self):
        """初始化资源"""
        resources.initialize()
        logger.info("资源管理器初始化完成")

        # 初始化游戏配置
        from core.config import GameConfig
        GameConfig.initialize()
        logger.info("游戏配置初始化完成")

        # 检查地图数据是否正确加载
        import os
        import json

        if len(GameConfig.MAPS_DATA) == 0:
            logger.error("GameConfig.MAPS_DATA为空，这是一个严重问题，游戏可能无法正常运行")

            # 记录环境信息以便调试
            logger.error(f"当前工作目录: {os.getcwd()}")
            data_path = 'data'
            configs_path = 'data/configs'
            maps_path = 'data/configs/maps.json'

            # 检查目录是否存在
            if os.path.exists(data_path):
                logger.error(f"data目录存在，内容: {os.listdir(data_path)}")
                if os.path.exists(configs_path):
                    logger.error(f"configs目录存在，内容: {os.listdir(configs_path)}")
                    if os.path.exists(maps_path):
                        logger.error(f"maps.json文件存在但加载失败，可能是文件格式有问题")
                        try:
                            # 尝试读取文件内容进行诊断
                            with open(maps_path, 'r', encoding='utf-8') as f:
                                file_content = f.read(1000)  # 读取前1000个字符用于诊断
                                logger.error(f"maps.json内容片段: {file_content}")
                        except Exception as read_error:
                            logger.error(f"无法读取maps.json内容: {read_error}")
                    else:
                        logger.error(f"maps.json文件不存在")
                else:
                    logger.error(f"configs目录不存在")
            else:
                logger.error(f"data目录不存在")

            # 尝试访问父级目录
            parent_dir = os.path.abspath('..')
            logger.error(f"父级目录: {parent_dir}")
            if os.path.exists(parent_dir):
                logger.error(f"父级目录内容: {os.listdir(parent_dir)}")

            # 警告用户
            logger.error("游戏可能无法正常加载地图数据，将使用默认地图")

        # 设置图标
        icon = resources.load_image("assets/images/icon.png")
        if icon:
            pygame.display.set_icon(icon)

    def _create_screens(self):
        """创建游戏界面"""
        try:
            logger.warning("开始创建游戏界面")

            # 创建主菜单
            main_menu = MainMenu(self.ui_manager, self.game)
            self.ui_manager.screens["main_menu"] = main_menu

            # 创建角色创建界面
            character_creation = CharacterCreation(self.ui_manager, self.game)
            self.ui_manager.screens["character_creation"] = character_creation

            # 创建加载游戏界面
            load_game = LoadGame(self.ui_manager, self.game, self)
            self.ui_manager.screens["load_game"] = load_game

            # 创建设置界面
            settings = Settings(self.ui_manager, self.game)
            self.ui_manager.screens["settings"] = settings

            # 创建游戏主界面
            game_screen = GameScreen(self.ui_manager, self.game)
            self.ui_manager.screens["game"] = game_screen

            # 设置游戏UI
            self.game.ui = self.ui_manager

            # 创建可能依赖游戏主界面的其他屏幕

            # 创建地图界面
            try:
                map_screen = MapScreen(self.ui_manager, self.game)
                self.ui_manager.screens["map"] = map_screen
            except Exception as e:
                logger.error(f"创建地图界面失败: {e}", exc_info=True)

            # 创建技能界面
            try:
                skills_screen = SkillsScreen(self.ui_manager, self.game)
                self.ui_manager.screens["skills"] = skills_screen
            except Exception as e:
                logger.error(f"创建技能界面失败: {e}")

            # 创建成就界面
            try:
                achievements_screen = AchievementsScreen(self.ui_manager, self.game)
                self.ui_manager.screens["achievements"] = achievements_screen
            except Exception as e:
                logger.error(f"创建成就界面失败: {e}")

            # 创建背包界面
            try:
                inventory_screen = InventoryScreen(self.ui_manager, self.game)
                self.ui_manager.screens["inventory"] = inventory_screen
            except Exception as e:
                logger.error(f"创建背包界面失败: {e}", exc_info=True)

            # 创建商店界面
            try:
                shop_screen = ShopScreen(self.ui_manager, self.game)
                self.ui_manager.screens["shop"] = shop_screen
            except Exception as e:
                logger.error(f"创建商店界面失败: {e}", exc_info=True)

            # 创建装备界面
            try:
                equipment_screen = EquipmentScreen(self.ui_manager, self.game)
                self.ui_manager.screens["equipment"] = equipment_screen
            except Exception as e:
                logger.error(f"创建装备界面失败: {e}", exc_info=True)

            # 创建VIP界面
            try:
                vip_screen = VIPScreen(self.ui_manager, self.game)
                self.ui_manager.screens["vip"] = vip_screen
            except Exception as e:
                logger.error(f"创建VIP界面失败: {e}", exc_info=True)

            # 创建签到界面
            try:
                signin_screen = SignInScreen(self.ui_manager, self.game)
                self.ui_manager.screens["signin"] = signin_screen
            except Exception as e:
                logger.error(f"创建签到界面失败: {e}", exc_info=True)

            # 创建战斗统计界面
            try:
                battle_stats_screen = BattleStatsScreen(self.ui_manager, self.game)
                self.ui_manager.screens["battle_stats"] = battle_stats_screen
            except Exception as e:
                logger.error(f"创建战斗统计界面失败: {e}", exc_info=True)

            # 创建登录界面
            try:
                login_screen = LoginScreen(self.ui_manager, self.game)
                self.ui_manager.screens["login"] = login_screen
            except Exception as e:
                logger.error(f"创建登录界面失败: {e}", exc_info=True)

            # 创建在线角色创建界面
            try:
                character_creation_screen = CharacterCreationScreen(self.ui_manager, self.game)
                self.ui_manager.screens["character_creation_online"] = character_creation_screen
            except Exception as e:
                logger.error(f"创建在线角色创建界面失败: {e}", exc_info=True)

            # 显示主菜单
            self.ui_manager.show_screen("main_menu")
            logger.warning("游戏界面创建完成")

        except Exception as e:
            logger.error(f"创建游戏界面过程中发生错误: {e}", exc_info=True)
            # 考虑在这里也显示一个错误对话框
            # self.current_dialog = MessageDialog(f"界面创建失败: {e}", self.screen.get_size())

    def run(self):
        """运行游戏主循环"""
        logger.info("开始游戏主循环")

        while self.running:
            # 计算帧间时间（以秒为单位）
            dt = self.clock.tick(self.fps) / 1000.0

            # 处理事件 (包括触发和处理对话框输入)
            self._handle_events()

            # 检查并处理对话框结果 (如果对话框刚刚关闭)
            self._check_dialog_result()

            # 只有当游戏仍在运行时才更新和渲染 (例如，退出未被确认)
            if self.running:
                # 更新游戏状态
                self._update(dt)

                # 渲染游戏画面 (包括绘制活动对话框)
                self._render()

        # 清理资源并退出 (只有当 self.running 变为 False 时执行)
        self._cleanup()

    def _handle_events(self):
        """处理游戏事件，优先处理活动对话框的事件"""
        for event in pygame.event.get():
            # 始终首先检查退出事件
            if event.type == pygame.QUIT:
                # 如果没有活动的对话框，则显示退出确认
                if not self.current_dialog or not self.current_dialog.is_active:
                    screen_size = self.screen.get_size()
                    self.current_dialog = ConfirmDialog("确定要退出游戏吗？", screen_size)
                    logger.info("显示退出确认对话框")
                # 不再立即设置 self.running = False，等待对话框结果
                continue # 跳过此事件的其他处理

            # 如果有活动对话框，将事件传递给它处理
            if self.current_dialog and self.current_dialog.is_active:
                self.current_dialog.handle_event(event)
                continue # 活动对话框优先处理事件，跳过后续处理

            # --- 仅在没有活动对话框时处理以下事件 ---
            if event.type == pygame.KEYDOWN:
                # 检测老板键 (Alt+1)
                if event.key == pygame.K_1 and pygame.key.get_mods() & pygame.KMOD_ALT:
                    # 切换窗口可见性
                    self._toggle_window_visibility()
                # 检测 Ctrl+1 快捷键，最小化到系统托盘
                elif event.key == pygame.K_1 and pygame.key.get_mods() & pygame.KMOD_CTRL:
                    # 如果支持系统托盘，则最小化到托盘
                    if TRAY_AVAILABLE and sys.platform == "win32":
                        self._minimize_to_tray()
                        logger.info("使用Ctrl+1快捷键最小化到系统托盘")
                    else:
                        logger.warning("系统托盘功能不可用，无法最小化到托盘")
                elif event.key == pygame.K_ESCAPE:
                    # ESC键打开菜单 (仅在游戏主界面)
                    if self.ui_manager.active_screen == "game":
                        # 确保 game 屏幕存在并且有 _on_menu_click 方法
                        if "game" in self.ui_manager.screens and hasattr(self.ui_manager.screens["game"], "_on_menu_click"):
                            self.ui_manager.screens["game"]._on_menu_click()
                        else:
                            logger.warning("无法在当前状态下通过ESC打开菜单")
                elif event.key == pygame.K_F11:
                    # F11切换全屏
                    self._toggle_fullscreen()

            # 将事件传递给UI管理器 (仅在没有活动对话框时)
            self.ui_manager.handle_event(event)

    def _check_dialog_result(self):
        """检查并处理当前对话框的结果（如果它刚刚关闭）"""
        if self.current_dialog and not self.current_dialog.is_active:
            result = self.current_dialog.get_result()
            logger.info(f"对话框 '{type(self.current_dialog).__name__}' 关闭，结果: {result}")

            # 根据对话框类型和结果执行操作
            if isinstance(self.current_dialog, ConfirmDialog):
                 # 假设这是退出确认对话框（需要更明确的机制来区分不同确认框）
                 if result is True:
                     self.running = False # 用户确认退出
                     logger.info("用户确认退出游戏")
                 else:
                     logger.info("用户取消退出")

            self.current_dialog = None

    def _update(self, dt: float):
        """更新游戏状态"""
        # 更新游戏逻辑
        self.game.update(dt)

        # 更新UI
        self.ui_manager.update(dt)

    def play_sound(self, sound_name: str, volume: float = 1.0, loops: int = 0):

        if not hasattr(self, 'audio_available') or not self.audio_available:
            return

        try:
            sound = resources.get_sound(sound_name)
            if sound:
                sound.set_volume(volume)
                sound.play(loops)
        except Exception as e:
            logger.warning(f"播放音效'{sound_name}'失败: {e}")

    def play_music(self, music_name: str, volume: float = 0.5, loops: int = -1):

        if not hasattr(self, 'audio_available') or not self.audio_available:
            return

        try:
            pygame.mixer.music.load(resources.get_music_path(music_name))
            pygame.mixer.music.set_volume(volume)
            pygame.mixer.music.play(loops)
        except Exception as e:
            logger.warning(f"播放音乐'{music_name}'失败: {e}")
            # 设置标志表示音频已不可用
            self.audio_available = False

    def _render(self):
        """渲染游戏画面"""
        # 清空屏幕
        self.screen.fill((0, 0, 0))

        # 绘制UI
        self.ui_manager.draw(self.screen)

        # 绘制活动的对话框 (覆盖在UI之上)
        if self.current_dialog and self.current_dialog.is_active:
            self.current_dialog.draw(self.screen)

        # 更新显示
        pygame.display.flip()

    def _toggle_fullscreen(self):
        """切换全屏显示模式"""
        self.fullscreen = not self.fullscreen
        flags = pygame.FULLSCREEN if self.fullscreen else 0
        self.screen = pygame.display.set_mode((self.screen_width, self.screen_height), flags)
        logger.info(f"切换全屏模式: {self.fullscreen}")

    def _init_tray_icon(self):
        """初始化系统托盘图标"""
        if not TRAY_AVAILABLE:
            logger.warning("系统托盘功能不可用，未安装pystray或PIL库")
            return

        try:
            # 如果已经有托盘图标，先停止它
            if self.tray_icon:
                try:
                    self.tray_icon.stop()
                    logger.info("停止现有托盘图标")
                except:
                    pass
                self.tray_icon = None

            # 直接加载原始游戏图标
            try:
                # 获取图标文件的完整路径
                icon_path = resource_path("assets/images/icon.png")
                logger.info(f"尝试加载图标文件: {icon_path}")

                if os.path.exists(icon_path):
                    # 使用PIL直接加载图标文件
                    icon_image = Image.open(icon_path)
                    logger.info(f"成功加载原始游戏图标，大小: {icon_image.size}")
                else:
                    # 尝试使用pygame加载图标
                    icon = resources.load_image("assets/images/icon.png")
                    if icon:
                        # 将pygame Surface转换为PIL Image
                        icon_data = pygame.image.tobytes(icon, 'RGBA')
                        icon_size = icon.get_size()
                        icon_image = Image.frombytes('RGBA', icon_size, icon_data)
                        logger.info(f"通过pygame加载原始游戏图标，大小: {icon_size}")
                    else:
                        # 创建一个简单的默认图标
                        icon_image = Image.new('RGB', (64, 64), color = (0, 128, 255))
                        logger.info("无法加载原始图标，使用蓝色替代图标")
            except Exception as e:
                logger.error(f"获取图标失败: {e}")
                logger.error(traceback.format_exc())
                # 创建一个简单的默认图标
                icon_image = Image.new('RGB', (64, 64), color = (0, 128, 255))
                logger.info("获取图标出错，使用蓝色替代图标")

            # 创建托盘图标菜单
            menu = pystray.Menu(
                pystray.MenuItem("恢复窗口", self._on_tray_restore),
                pystray.MenuItem("退出游戏", self._on_tray_exit)
            )

            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "game_tray_icon",
                icon_image,
                "萝卜放置传奇",
                menu
            )

            # 设置点击托盘图标的行为
            self.tray_icon.on_click = self._on_tray_click

            logger.info("系统托盘图标初始化完成")
            return True
        except Exception as e:
            logger.error(f"初始化系统托盘图标失败: {e}")
            logger.error(traceback.format_exc())
            self.tray_icon = None
            return False

    def _on_tray_click(self, icon, data):  # pylint: disable=unused-argument
        """托盘图标点击事件处理"""
        # 左键点击恢复窗口
        if data.button == 1:  # 左键
            self._restore_from_tray()

    def _on_tray_restore(self, icon, item):  # pylint: disable=unused-argument
        """托盘菜单恢复窗口"""
        self._restore_from_tray()

    def _on_tray_exit(self, icon, item):  # pylint: disable=unused-argument
        """托盘菜单退出游戏"""
        logger.info("用户从托盘菜单选择退出游戏")

        try:
            # 恢复窗口（如果已最小化到托盘）
            if self.is_minimized_to_tray:
                try:
                    # 显示窗口
                    if sys.platform == "win32":
                        window_handle = pygame.display.get_wm_info().get('window')
                        if window_handle:
                            # 显示窗口 (SW_SHOW = 5)
                            ctypes.windll.user32.ShowWindow(window_handle, 5)
                except Exception as e:
                    logger.error(f"退出前恢复窗口失败: {e}")

                self.is_minimized_to_tray = False

            # 停止托盘图标
            if self.tray_icon:
                self.tray_icon.stop()
                self.tray_icon = None
                self.tray_thread = None
                logger.info("托盘图标已停止")

            # 设置游戏退出标志
            self.running = False

        except Exception as e:
            logger.error(f"从托盘菜单退出游戏失败: {e}")
            logger.error(traceback.format_exc())
            # 强制设置退出标志
            self.running = False

    def _minimize_to_tray(self):
        """最小化到系统托盘"""
        if not TRAY_AVAILABLE:
            logger.warning("系统托盘功能不可用")
            return

        if self.is_minimized_to_tray:
            logger.info("窗口已经最小化到托盘")
            return

        # 确保托盘图标已初始化
        if not self.tray_icon:
            logger.info("托盘图标未初始化，重新初始化")
            self._init_tray_icon()
            if not self.tray_icon:
                logger.error("托盘图标初始化失败")
                return

        # 先启动托盘图标，再隐藏窗口
        try:
            # 停止之前的托盘线程（如果存在）
            if self.tray_thread and self.tray_thread.is_alive():
                if self.tray_icon:
                    self.tray_icon.stop()
                self.tray_thread = None
                logger.info("停止之前的托盘线程")

            # 创建新的托盘图标线程
            self.tray_thread = threading.Thread(target=self.tray_icon.run)
            self.tray_thread.daemon = True
            self.tray_thread.start()

            # 等待托盘图标显示
            time.sleep(0.5)
            logger.info("系统托盘图标已显示")

            # 标记为已最小化到托盘
            self.is_minimized_to_tray = True

            # 隐藏窗口
            if sys.platform == "win32":
                window_handle = pygame.display.get_wm_info().get('window')
                if window_handle:
                    # 隐藏窗口 (SW_HIDE = 0)
                    ctypes.windll.user32.ShowWindow(window_handle, 0)
                    logger.info("窗口已隐藏")

        except Exception as e:
            logger.error(f"最小化到托盘失败: {e}")
            logger.error(traceback.format_exc())

    def _restore_from_tray(self):
        """从系统托盘恢复窗口"""
        if not self.is_minimized_to_tray:
            logger.info("窗口未最小化到托盘，无需恢复")
            return

        try:
            # 先显示窗口
            if sys.platform == "win32":
                window_handle = pygame.display.get_wm_info().get('window')
                if window_handle:
                    # 显示并激活窗口 (SW_SHOW = 5)
                    ctypes.windll.user32.ShowWindow(window_handle, 5)
                    # 将窗口置于前台 (HWND_TOP = 0)
                    ctypes.windll.user32.SetForegroundWindow(window_handle)
                    logger.info("窗口已从托盘恢复并置于前台")

            # 标记为未最小化到托盘
            self.is_minimized_to_tray = False

            # 停止托盘图标（在单独的线程中执行，避免阻塞）
            def stop_tray():
                try:
                    if self.tray_icon:
                        self.tray_icon.stop()
                        logger.info("托盘图标已停止")
                except Exception as e:
                    logger.error(f"停止托盘图标失败: {e}")

            stop_thread = threading.Thread(target=stop_tray)
            stop_thread.daemon = True
            stop_thread.start()

            # 清理线程引用
            self.tray_thread = None

        except Exception as e:
            logger.error(f"从托盘恢复窗口失败: {e}")
            logger.error(traceback.format_exc())

    def _toggle_window_visibility(self):
        """切换窗口可见性（最小化/恢复）"""
        # 如果支持系统托盘，则最小化到托盘
        if TRAY_AVAILABLE and sys.platform == "win32":
            if not self.is_minimized_to_tray:
                self._minimize_to_tray()
            else:
                self._restore_from_tray()
            return

        # 不支持系统托盘时，使用普通的最小化/恢复
        if sys.platform == "win32":
            try:
                # 获取窗口句柄
                window_info = pygame.display.get_wm_info()
                if 'window' not in window_info:
                    logger.error("无法获取窗口句柄")
                    return

                window_handle = window_info['window']

                # 检查窗口是否最小化
                is_minimized = False

                # 检查是否已正确定义结构体
                if 'WINDOWPLACEMENT' in globals():
                    try:
                        placement = WINDOWPLACEMENT()
                        placement.length = ctypes.sizeof(placement)
                        result = ctypes.windll.user32.GetWindowPlacement(window_handle, ctypes.byref(placement))

                        if result:
                            # SW_SHOWMINIMIZED = 2
                            is_minimized = (placement.showCmd == 2)
                    except Exception as e:
                        logger.error(f"获取窗口状态失败: {e}")

                # 根据窗口状态切换可见性
                if not is_minimized:
                    # 最小化窗口 (SW_MINIMIZE = 6)
                    ctypes.windll.user32.ShowWindow(window_handle, 6)
                    logger.info("窗口已最小化到任务栏")
                else:
                    # 恢复窗口 (SW_RESTORE = 9)
                    ctypes.windll.user32.ShowWindow(window_handle, 9)
                    logger.info("窗口已从任务栏恢复")
            except Exception as e:
                logger.error(f"切换窗口可见性失败: {e}")
                logger.error(traceback.format_exc())
                # 尝试使用简单方法最小化
                try:
                    window_handle = pygame.display.get_wm_info().get('window')
                    if window_handle:
                        ctypes.windll.user32.ShowWindow(window_handle, 6)  # SW_MINIMIZE = 6
                        logger.info("已使用备用方法最小化窗口")
                except Exception as e2:
                    logger.error(f"备用最小化也失败: {e2}")
        else:
            # 非Windows系统
            logger.warning("窗口可见性切换功能仅在Windows系统上可用")

    def _cleanup(self, exit_game=True):
        """清理资源并退出"""
        # 保存游戏数据
        if self.game.player:
            self.game.save_game()

        # 恢复窗口（如果已最小化到托盘）
        if self.is_minimized_to_tray:
            try:
                # 显示窗口
                if sys.platform == "win32":
                    window_handle = pygame.display.get_wm_info().get('window')
                    if window_handle:
                        # 显示窗口 (SW_SHOW = 5)
                        ctypes.windll.user32.ShowWindow(window_handle, 5)
            except Exception as e:
                logger.error(f"退出前恢复窗口失败: {e}")

            self.is_minimized_to_tray = False

        # 清理系统托盘图标
        if hasattr(self, 'tray_icon') and self.tray_icon:
            try:
                # 直接停止托盘图标
                self.tray_icon.stop()
                self.tray_icon = None
                self.tray_thread = None
                logger.info("系统托盘图标已清理")
            except Exception as e:
                logger.error(f"清理系统托盘图标失败: {e}")
                logger.error(traceback.format_exc())

        # 清理资源
        resources.cleanup()

        # 退出Pygame
        pygame.quit()
        if exit_game:
            logger.info("游戏已退出")
        else:
            logger.info("清理资源完成（不退出）")

    def load_game(self, game_data):
        """加载游戏存档"""
        result = self.game._load_from_data(game_data)
        if result:
            logger.info("成功加载游戏，切换到游戏界面")
            self.ui_manager.show_screen("game")

            # 确保游戏界面正确初始化
            if "game" in self.ui_manager.screens:
                game_screen = self.ui_manager.screens["game"]
                if hasattr(game_screen, "initialize"):
                    game_screen.initialize()
                    logger.info("游戏界面初始化完成")

            # 商店界面已在创建时注册，不需要重复注册
            logger.info("游戏加载完成")

            return True
        else:
            logger.error("加载游戏失败")
            return False

    def load_game_from_data(self, game_data):
        """加载游戏存档数据(兼容性方法)"""
        logger.info("使用load_game_from_data方法加载游戏存档数据")
        result = self.game.load_game_from_data(game_data)
        if result:
            logger.info("成功加载游戏，切换到游戏界面")
            self.ui_manager.show_screen("game")

            # 确保游戏界面正确初始化
            if "game" in self.ui_manager.screens:
                game_screen = self.ui_manager.screens["game"]
                if hasattr(game_screen, "initialize"):
                    game_screen.initialize()
                    logger.info("游戏界面初始化完成")

            logger.info("游戏加载完成")
            return True
        else:
            logger.error("加载游戏存档失败")
            return False

# 创建全局游戏实例供其他模块导入
game_instance = None

def main():
    """游戏入口函数"""
    global game_instance
    try:
        # 创建游戏实例并运行
        game_instance = GameApp()
        game_instance.run() # run() 现在包含清理逻辑

    except Exception as e:
        error_str = str(e)
        logger.error(f"游戏发生未处理的异常: {e}", exc_info=True)

        # 尝试在无声模式下启动 (如果错误与音频相关)
        if "WASAPI" in error_str and "audio endpoint" in error_str and game_instance is None:
            logger.warning("检测到音频初始化错误，尝试在无声模式下启动游戏")
            try:
                game_instance = GameApp() # GameApp init handles audio failure
                game_instance.run()
                sys.exit(0) # 成功运行后正常退出
            except Exception as retry_error:
                logger.error(f"无声模式启动游戏也失败: {retry_error}", exc_info=True)
                e = retry_error # 使用重试后的错误信息

        # 显示错误对话框 (使用新的 MessageDialog)
        if pygame.display.get_init():
            screen = pygame.display.get_surface()
            if screen:
                screen_size = screen.get_size()
                error_dialog = MessageDialog(f"游戏发生严重错误，请查看日志: {str(e)[:100]}...", screen_size) # 限制错误信息长度
                error_running = True
                error_clock = pygame.time.Clock()

                while error_running:
                    for event in pygame.event.get():
                        if event.type == pygame.QUIT:
                            error_running = False
                        if error_dialog.is_active:
                            error_dialog.handle_event(event)
                        else: # 对话框被关闭
                            error_running = False

                    screen.fill((20, 20, 20)) # 深色背景
                    if error_dialog.is_active:
                        error_dialog.draw(screen)

                    pygame.display.flip()
                    error_clock.tick(30) # 错误界面不需要高帧率

        # 退出前尝试清理资源
        try:
            if game_instance:
                 # 调用清理但不退出程序，因为我们即将 sys.exit(1)
                 game_instance._cleanup(exit_game=False)
            else:
                 # 如果 game_instance 都没创建成功，直接退出 pygame
                 pygame.quit()
        except Exception as cleanup_error:
            logger.error(f"退出前清理资源时发生错误: {cleanup_error}", exc_info=True)

        sys.exit(1) # 发生错误，以非零状态码退出

if __name__ == "__main__":
    main()
