import random
import json
import os
import logging
import time
from pathlib import Path
from copy import deepcopy
from collections import defaultdict, Counter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DropRateTester:
    def __init__(self, monster_name, test_times=10000):
        """
        初始化掉落率测试器
        
        Args:
            monster_name: 要测试的怪物名称
            test_times: 测试次数，默认10000次以确保低概率掉落也能被检测
        """
        self.monster_name = monster_name
        self.test_times = test_times
        self.drop_rates = {}
        self.equipment_db = {}
        self.dropped_items = defaultdict(int)
        self.expected_drops = {}
        self.all_configured_items = set()
        self.dropped_item_types = Counter()
        
        # 统计不同品质装备的掉落
        self.quality_drops = {
            "普通": 0,
            "精良": 0,
            "稀有": 0,
            "史诗": 0,
            "传说": 0
        }
        
        self.load_configs()
    
    def load_configs(self):
        """加载必要的游戏配置"""
        try:
            # 加载掉落率配置
            with open("data/configs/drop_rates.json", "r", encoding="utf-8") as f:
                data = json.load(f)
                self.drop_rates = data.get("drop_rates", {})
            
            # 加载装备配置
            with open("data/configs/equipment.json", "r", encoding="utf-8") as f:
                data = json.load(f)
                self.equipment_db = data.get("equipment_db", {})
                self.tiers_config = data.get("tiers", {})
            
            # 获取怪物配置的所有掉落物品
            if self.monster_name in self.drop_rates.get("monsters", {}):
                drops = self.drop_rates["monsters"][self.monster_name].get("drops", [])
                for drop in drops:
                    item_name = drop.get("item", "")
                    drop_rate = drop.get("rate", 0)
                    if item_name:
                        self.all_configured_items.add(item_name)
                        self.expected_drops[item_name] = drop_rate
            else:
                logger.error(f"找不到怪物 '{self.monster_name}' 的掉落配置")
        
        except Exception as e:
            logger.error(f"加载配置时出错: {e}")
            raise
    
    def get_item_info(self, item_name):
        """获取物品完整信息"""
        # 检查各个装备类别
        for category, items in self.equipment_db.items():
            if isinstance(items, list):
                for item in items:
                    if item.get("name") == item_name:
                        item_copy = deepcopy(item)
                        # 确保物品有type属性
                        if "type" not in item_copy:
                            item_copy["type"] = self._infer_item_type(item_name, category)
                        return item_copy
        
        return {"name": item_name, "type": self._infer_item_type(item_name)}
    
    def _infer_item_type(self, item_name, category=None):
        """根据物品名称或分类推断物品类型"""
        if category:
            if category in ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]:
                return category
            elif "技能书" in category:
                return "技能书"
            elif "消耗品" in category:
                return "消耗品"
        
        # 根据名称推断
        type_patterns = {
            "武器": ["剑", "刀", "杖", "斧", "弓", "匕首", "锤", "枪"],
            "防具": ["盔甲", "战衣", "布衣", "袍", "护甲", "铠甲"],
            "项链": ["项链", "明珠", "珠子", "护符", "符"],
            "戒指": ["戒指", "指环"],
            "手镯": ["手镯", "护腕", "手套", "臂环"],
            "头盔": ["头盔", "帽子", "头饰", "冠"],
            "技能书": ["技能书", "秘籍", "法术书", "咒语书", "弯刀", "冲撞", "剑法", "启示", "剑术", 
                   "战甲术", "电影", "魔法盾", "火墙", "盾", "火焰", "咆哮", "雷光", "隐身术", 
                   "移动", "诱惑", "地狱火", "火球", "火符", "言术", "魔咒", "治疗术", "神兽", "骷髅", "治愈术", "雷电术"],
            "消耗品": ["药水", "药", "丹", "符", "卷轴", "强化石", "祝福油", "战神油"]
        }
        
        for item_type, patterns in type_patterns.items():
            if any(pattern in item_name for pattern in patterns):
                return item_type
        
        return "未知物品"
    
    def _generate_equipment_quality(self, base_eq):
        """简化版的装备品质生成，模拟游戏中的品质生成逻辑"""
        # 复制装备数据
        base_eq = deepcopy(base_eq)
        
        # 获取品质配置
        tiers = list(self.tiers_config.keys()) or ["普通", "精良", "稀有", "史诗", "传说"]
        default_weights = [80, 15, 10, 4, 1]
        
        # 计算实际权重
        weights = [self.tiers_config.get(t, {}).get("weight", d) for t, d in zip(tiers, default_weights)]
        
        # 使用权重随机选择品质
        tier = random.choices(tiers, weights=weights, k=1)[0]
        
        # 添加品质信息
        base_eq.update({
            "tier": tier,
            "quality": tier
        })
        
        return base_eq
    
    def simulate_monster_drops(self):
        """模拟怪物掉落过程"""
        if not self.all_configured_items:
            logger.warning(f"怪物 '{self.monster_name}' 没有配置掉落物品")
            return
        
        # 模拟多次击杀怪物
        start_time = time.time()
        for i in range(self.test_times):
            if (i + 1) % 1000 == 0:
                elapsed = time.time() - start_time
                logger.info(f"已模拟 {i + 1} 次击杀... (用时: {elapsed:.2f}秒)")
            
            self.simulate_single_kill()
        
        # 分析结果
        self.analyze_results()
    
    def simulate_single_kill(self):
        """模拟单次击杀怪物的掉落"""
        if self.monster_name not in self.drop_rates.get("monsters", {}):
            return
        
        # 获取怪物掉落配置
        drops = self.drop_rates["monsters"][self.monster_name].get("drops", [])
        
        # 分类掉落项
        equipment_drops = []
        other_drops = []
        
        for drop in drops:
            item_name = drop.get("item", "")
            if not item_name:
                continue
            
            # 获取物品信息
            item = self.get_item_info(item_name)
            
            # 根据类型分类
            item_type = item.get("type", "")
            equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]
            is_equipment = any(eq_type in item_type for eq_type in equipment_types)
            
            if is_equipment:
                equipment_drops.append((drop, item))
            else:
                other_drops.append((drop, item))
        
        # 处理装备掉落（最多一件）
        if equipment_drops:
            # 全局调整因子，模拟游戏中的设置
            global_drop_rate_factor = 0.2
            
            # 计算总掉落率
            weights = [drop.get("rate", 0) * global_drop_rate_factor for drop, _ in equipment_drops]
            total_drop_chance = min(1.0, sum(weights))
            
            if random.random() < total_drop_chance:
                # 根据权重选择具体掉落哪件装备
                normalized_weights = [w/total_drop_chance for w in weights]
                selected_idx = random.choices(range(len(equipment_drops)), weights=normalized_weights, k=1)[0]
                drop, item = equipment_drops[selected_idx]
                
                # 应用装备品质生成
                item = self._generate_equipment_quality(item)
                
                # 记录掉落
                item_name = item.get("name", "")
                quality = item.get("tier", "普通")
                self.dropped_items[item_name] += 1
                self.dropped_item_types[item.get("type", "未知")] += 1
                self.quality_drops[quality] += 1
        
        # 处理非装备掉落
        for drop, item in other_drops:
            # 全局调整因子
            global_drop_rate_factor = 0.2
            
            # 计算掉落概率
            drop_chance = drop.get("rate", 0) * global_drop_rate_factor
            
            if random.random() < drop_chance:
                item_name = item.get("name", "")
                self.dropped_items[item_name] += 1
                self.dropped_item_types[item.get("type", "未知")] += 1
    
    def analyze_results(self):
        """分析测试结果"""
        logger.info(f"\n{'='*50}")
        logger.info(f"怪物 '{self.monster_name}' 掉落测试结果 (测试次数: {self.test_times})")
        logger.info(f"{'='*50}")
        
        # 检查所有配置物品是否都掉落过
        dropped_items_set = set(self.dropped_items.keys())
        missing_items = self.all_configured_items - dropped_items_set
        
        logger.info(f"配置物品总数: {len(self.all_configured_items)}")
        logger.info(f"实际掉落物品总数: {len(dropped_items_set)}")
        
        if missing_items:
            logger.warning(f"未掉落的配置物品 ({len(missing_items)}): {', '.join(missing_items)}")
        else:
            logger.info("已确认所有配置物品都有掉落")
        
        # 显示掉落率
        logger.info(f"\n掉落详情:")
        logger.info(f"{'-'*50}")
        logger.info(f"{'物品名称':<20} {'配置掉落率':<15} {'实际掉落率':<15} {'差异':<10}")
        logger.info(f"{'-'*50}")
        
        for item_name in sorted(self.all_configured_items):
            expected_rate = self.expected_drops.get(item_name, 0)
            actual_drops = self.dropped_items.get(item_name, 0)
            actual_rate = actual_drops / self.test_times
            rate_diff = actual_rate - expected_rate
            
            logger.info(f"{item_name:<20} {expected_rate:.6f}      {actual_rate:.6f}      {rate_diff:.6f}")
        
        # 显示掉落类型统计
        logger.info(f"\n物品类型统计:")
        logger.info(f"{'-'*50}")
        for item_type, count in self.dropped_item_types.most_common():
            percentage = count / sum(self.dropped_item_types.values()) * 100
            logger.info(f"{item_type:<15}: {count} ({percentage:.2f}%)")
        
        # 显示装备品质统计
        total_equipment = sum(self.quality_drops.values())
        if total_equipment > 0:
            logger.info(f"\n装备品质统计:")
            logger.info(f"{'-'*50}")
            for quality, count in self.quality_drops.items():
                percentage = count / total_equipment * 100
                logger.info(f"{quality:<10}: {count} ({percentage:.2f}%)")
        
        logger.info(f"{'='*50}")
        
        # 保存结果到文件
        self.save_results_to_file()
    
    def save_results_to_file(self):
        """将测试结果保存到文件"""
        output_dir = Path("test_results")
        output_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = output_dir / f"{self.monster_name}_drop_test_{timestamp}.txt"
        
        with open(filename, "w", encoding="utf-8") as f:
            f.write(f"怪物 '{self.monster_name}' 掉落测试结果\n")
            f.write(f"测试次数: {self.test_times}\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'-'*50}\n\n")
            
            # 配置物品总数
            f.write(f"配置物品总数: {len(self.all_configured_items)}\n")
            f.write(f"实际掉落物品总数: {len(set(self.dropped_items.keys()))}\n\n")
            
            # 未掉落物品
            missing_items = self.all_configured_items - set(self.dropped_items.keys())
            if missing_items:
                f.write(f"未掉落的配置物品 ({len(missing_items)}):\n")
                for item in sorted(missing_items):
                    f.write(f"- {item}\n")
            else:
                f.write("已确认所有配置物品都有掉落\n")
            
            f.write(f"\n{'-'*50}\n")
            f.write(f"掉落详情:\n")
            f.write(f"{'物品名称':<20} {'配置掉落率':<15} {'实际掉落率':<15} {'差异':<10}\n")
            f.write(f"{'-'*50}\n")
            
            for item_name in sorted(self.all_configured_items):
                expected_rate = self.expected_drops.get(item_name, 0)
                actual_drops = self.dropped_items.get(item_name, 0)
                actual_rate = actual_drops / self.test_times
                rate_diff = actual_rate - expected_rate
                
                f.write(f"{item_name:<20} {expected_rate:.6f}      {actual_rate:.6f}      {rate_diff:.6f}\n")
            
            # 物品类型统计
            f.write(f"\n{'-'*50}\n")
            f.write(f"物品类型统计:\n")
            for item_type, count in self.dropped_item_types.most_common():
                percentage = count / sum(self.dropped_item_types.values()) * 100
                f.write(f"{item_type:<15}: {count} ({percentage:.2f}%)\n")
            
            # 装备品质统计
            total_equipment = sum(self.quality_drops.values())
            if total_equipment > 0:
                f.write(f"\n{'-'*50}\n")
                f.write(f"装备品质统计:\n")
                for quality, count in self.quality_drops.items():
                    percentage = count / total_equipment * 100
                    f.write(f"{quality:<10}: {count} ({percentage:.2f}%)\n")
        
        logger.info(f"测试结果已保存到: {filename}")

def test_specific_monster(monster_name, test_times=10000):
    """测试特定怪物的掉落"""
    tester = DropRateTester(monster_name, test_times)
    tester.simulate_monster_drops()

def test_all_monsters(test_times=10000):
    """测试所有怪物的掉落"""
    # 加载掉落率配置
    try:
        with open("data/configs/drop_rates.json", "r", encoding="utf-8") as f:
            data = json.load(f)
            monsters = data.get("drop_rates", {}).get("monsters", {}).keys()
    except Exception as e:
        logger.error(f"加载配置时出错: {e}")
        return
    
    logger.info(f"发现 {len(monsters)} 个怪物，开始测试...")
    
    for i, monster_name in enumerate(monsters):
        logger.info(f"[{i+1}/{len(monsters)}] 测试怪物: {monster_name}")
        test_specific_monster(monster_name, test_times)

def main():
    """主函数"""
    print("怪物掉落测试工具")
    print("="*30)
    print("1. 测试单个怪物")
    print("2. 测试所有怪物")
    print("="*30)
    
    choice = input("请选择测试模式 (1/2): ")
    
    if choice == "1":
        monster_name = input("请输入要测试的怪物名称 (例如: 半兽人): ")
        test_times = input("请输入测试次数 (建议10000或更多，直接回车默认10000): ")
        test_times = int(test_times) if test_times else 10000
        
        test_specific_monster(monster_name, test_times)
    elif choice == "2":
        test_times = input("请输入每个怪物的测试次数 (建议5000或更多，直接回车默认5000): ")
        test_times = int(test_times) if test_times else 5000
        
        test_all_monsters(test_times)
    else:
        print("无效选择!")

if __name__ == "__main__":
    main() 