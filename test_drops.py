#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
怪物掉落测试脚本
用于验证物品掉落系统及品质随机生成机制是否正常工作
"""

import os
import sys
import json
import random
from collections import Counter, defaultdict

# 添加项目根目录到系统路径，确保能导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.logger import logger
    from core.game import GameConfig
    from core.game import Game
    from core.player import Player
    from core.monster import Monster
    
    # 初始化游戏配置
    GameConfig.initialize()
    
    # 创建测试用玩家
    player = Player(character_class="战士", name="测试玩家")
    player.level = 30  # 设置较高等级以测试多种掉落
    player.inventory = []
    
    # 创建游戏实例
    game = Game()
    # 设置测试玩家
    game.player = player
    
    # 需要测试的怪物列表
    test_monsters = [
        "半兽人",    # 低级怪物
        "僵尸",      # 中级怪物
        "尸王",      # 高级怪物带技能书
        "触龙神"     # 高级精英怪物
    ]
    
    # 测试参数
    kills_per_monster = 500  # 每种怪物击杀次数
    
    # 统计数据
    drop_stats = {}
    quality_stats = defaultdict(Counter)
    total_drops = 0
    total_kills = 0
    
    print("开始测试怪物掉落系统...")
    
    # 测试每种怪物的掉落
    for monster_name in test_monsters:
        print(f"\n测试怪物: {monster_name}")
        drop_stats[monster_name] = {
            "kills": 0,
            "total_drops": 0,
            "equipment_drops": 0,
            "qualities": Counter(),
            "items": Counter()
        }
        
        # 创建怪物实例
        monster_data = GameConfig.MONSTER_DATA.get(monster_name)
        if not monster_data:
            print(f"错误: 找不到怪物 {monster_name} 的数据")
            continue
            
        # 获取怪物掉落配置
        monster_drops = GameConfig.DROP_RATES.get("monsters", {}).get(monster_name, {}).get("drops", [])
        if not monster_drops:
            print(f"警告: 怪物 {monster_name} 没有配置掉落物品")
        
        # --- 修改：使用列表索引访问怪物数据 --- 
        try:
            # 假设列表索引顺序: [等级, 血量, 物防, 魔防, [最小攻击, 最大攻击], 命中, 攻速, 经验值]
            monster_level = monster_data[0]
            monster_hp = monster_data[1]
            monster_defense = monster_data[2]
            monster_magic_defense = monster_data[3]
            monster_attack_range_list = monster_data[4]
            monster_attack_range = {"min": monster_attack_range_list[0], "max": monster_attack_range_list[1]}
            monster_accuracy = monster_data[5]
            monster_attack_speed = monster_data[6]
            monster_exp = monster_data[7] if len(monster_data) > 7 else monster_level * 10 # 兼容可能没有经验值的情况
        except (IndexError, TypeError) as e:
            print(f"错误: 解析怪物 {monster_name} 的数据列表时出错: {e}")
            print(f"原始数据: {monster_data}")
            continue
            
        # 显示怪物基本信息
        print(f"  - 等级: {monster_level}")
        print(f"  - 配置的掉落物品数量: {len(monster_drops)}")
        
        # 清空玩家背包便于统计
        player.inventory = []
        
        # 模拟多次击杀
        for i in range(kills_per_monster):
            # 创建怪物实例
            monster = Monster(
                name=monster_name,
                level=monster_level,
                hp=monster_hp,
                defense=monster_defense,
                magic_defense=monster_magic_defense,
                attack_range=monster_attack_range,
                exp=monster_exp,
                attack_speed=monster_attack_speed
            )
            
            # 记录背包大小前
            before_size = len(player.inventory)
            
            # 处理怪物死亡和掉落
            game.current_enemy = monster  # 设置当前敌人为此怪物
            game.handle_monster_death()  # 不传入参数
            
            # 统计掉落
            after_size = len(player.inventory)
            drops_count = after_size - before_size
            
            # 更新统计
            drop_stats[monster_name]["kills"] += 1
            drop_stats[monster_name]["total_drops"] += drops_count
            
            # 如果有掉落，分析掉落物品
            if drops_count > 0:
                # 分析新掉落的物品
                for idx in range(before_size, after_size):
                    item = player.inventory[idx]
                    item_name = item.get("name", "未知物品")
                    item_type = item.get("type", "")
                    
                    # 记录物品名称
                    drop_stats[monster_name]["items"][item_name] += 1
                    
                    # 检查是否是装备
                    equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]
                    if any(eq_type in item_type for eq_type in equipment_types):
                        drop_stats[monster_name]["equipment_drops"] += 1
                        
                        # 记录装备品质
                        quality = item.get("tier", "普通")
                        drop_stats[monster_name]["qualities"][quality] += 1
                        quality_stats[monster_name][quality] += 1
            
            # 每100次输出进度
            if (i + 1) % 100 == 0:
                print(f"  - 已击杀 {i + 1} 次...")
        
        # 清理本轮测试的背包
        player.inventory = []
        
        # 计算本怪物的掉落率
        kills = drop_stats[monster_name]["kills"]
        total_monster_drops = drop_stats[monster_name]["total_drops"]
        total_drops += total_monster_drops
        total_kills += kills
        
        drop_rate = total_monster_drops / kills if kills > 0 else 0
        equipment_rate = drop_stats[monster_name]["equipment_drops"] / kills if kills > 0 else 0
        
        print(f"  击杀 {kills} 次，总计掉落 {total_monster_drops} 件物品")
        print(f"  平均每次击杀掉落 {drop_rate:.2f} 件物品")
        print(f"  装备掉落率: {equipment_rate:.2%}")
        
        # 输出品质分布
        if drop_stats[monster_name]["equipment_drops"] > 0:
            print("  装备品质分布:")
            for quality, count in drop_stats[monster_name]["qualities"].items():
                quality_rate = count / drop_stats[monster_name]["equipment_drops"]
                print(f"    - {quality}: {count} 件 ({quality_rate:.2%})")
        
        # 输出前10常见掉落物品
        if drop_stats[monster_name]["items"]:
            print("  常见掉落物品(前10):")
            for item_name, count in drop_stats[monster_name]["items"].most_common(10):
                item_rate = count / kills
                print(f"    - {item_name}: {count} 件 ({item_rate:.2%} 掉落率)")
    
    # 输出总体统计
    print("\n===== 总体统计 =====")
    print(f"总击杀数: {total_kills} 次")
    print(f"总掉落物品: {total_drops} 件")
    print(f"平均每次击杀掉落: {total_drops/total_kills:.2f} 件物品")
    
    # 所有怪物的品质统计
    all_qualities = Counter()
    all_equipment_drops = 0
    
    for monster_name, stats in drop_stats.items():
        all_equipment_drops += stats["equipment_drops"]
        for quality, count in stats["qualities"].items():
            all_qualities[quality] += count
    
    if all_equipment_drops > 0:
        print("\n装备品质分布(所有怪物):")
        for quality in ["普通", "精良", "稀有", "史诗", "传说"]:
            count = all_qualities[quality]
            quality_rate = count / all_equipment_drops if all_equipment_drops > 0 else 0
            print(f"  - {quality}: {count} 件 ({quality_rate:.2%})")
    
    print("\n测试完成!")

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
except Exception as e:
    print(f"测试过程中发生错误: {e}")
    import traceback
    print(traceback.format_exc()) 