#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
装备品质生成测试脚本
用于验证物品掉落后品质随机生成机制是否正常工作
"""

import os
import sys
import json
import random
from collections import Counter
import matplotlib.pyplot as plt

# 添加项目根目录到系统路径，确保能导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.logger import logger
    from core.game import GameConfig
    from core.game import Game
    
    # 初始化游戏配置
    GameConfig.initialize()
    
    # 创建测试用装备数据
    test_equipment = {
        "name": "测试武器",
        "type": "武器",
        "level": 1,
        "attack": [5, 10],
        "tier": "普通"
    }
    
    # 创建游戏实例
    game = Game()
    
    # 测试参数
    test_count = 10000  # 测试次数
    
    # 记录测试结果
    quality_counter = Counter()
    bonus_distribution = {}
    
    print("开始测试装备品质生成系统...")
    print(f"将生成 {test_count} 个测试装备")
    
    # 进行多次测试
    for i in range(test_count):
        result = game._generate_equipment_quality(test_equipment.copy())
        
        # 记录品质结果
        quality = result.get("tier", "未知")
        quality_counter[quality] += 1
        
        # 记录属性加成
        if quality not in bonus_distribution:
            bonus_distribution[quality] = []
        
        quality_bonus = result.get("quality_bonus", {})
        total_bonus = sum(quality_bonus.values())
        bonus_distribution[quality].append(total_bonus)
    
    # 输出测试结果
    print("\n品质分布统计:")
    for quality, count in quality_counter.items():
        percentage = (count / test_count) * 100
        print(f"{quality}: {count} 件 ({percentage:.2f}%)")
    
    # 计算每种品质的平均加成点数
    print("\n品质加成点数统计:")
    for quality, bonuses in bonus_distribution.items():
        if bonuses:
            avg_bonus = sum(bonuses) / len(bonuses)
            print(f"{quality}: 平均 {avg_bonus:.2f} 点加成")
    
    # 特殊属性统计（仅史诗和传说品质）
    if "史诗" in quality_counter or "传说" in quality_counter:
        print("\n特殊属性统计:")
        special_counter = Counter()
        
        # 重新测试收集特殊属性数据
        for i in range(min(1000, quality_counter.get("史诗", 0) + quality_counter.get("传说", 0))):
            result = game._generate_equipment_quality(test_equipment.copy())
            quality = result.get("tier", "未知")
            if quality in ["史诗", "传说"] and "special" in result:
                special_counter[f"{quality}-{result['special']}"] += 1
        
        for special, count in special_counter.items():
            print(f"{special}: {count} 件")
    
    # 可视化结果（如果有matplotlib）
    try:
        # 创建饼图
        labels = list(quality_counter.keys())
        sizes = list(quality_counter.values())
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        
        # 品质分布饼图
        ax1.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax1.axis('equal')
        ax1.set_title('装备品质分布')
        
        # 品质加成柱状图
        qualities = []
        avg_bonuses = []
        for quality in ["普通", "精良", "稀有", "史诗", "传说"]:
            if quality in bonus_distribution and bonus_distribution[quality]:
                qualities.append(quality)
                avg_bonuses.append(sum(bonus_distribution[quality]) / len(bonus_distribution[quality]))
        
        ax2.bar(qualities, avg_bonuses)
        ax2.set_title('各品质平均加成点数')
        ax2.set_ylabel('加成点数')
        
        plt.tight_layout()
        plt.savefig('quality_test_results.png')
        print("\n结果图表已保存为 quality_test_results.png")
    except Exception as e:
        print(f"无法创建可视化图表: {e}")
    
    print("\n测试完成!")

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
except Exception as e:
    print(f"测试过程中发生错误: {e}")
    import traceback
    print(traceback.format_exc()) 