#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志分析脚本
用于分析游戏日志中的品质生成相关信息
"""

import os
import sys
import re
from collections import Counter, defaultdict
import glob

def analyze_quality_distribution():
    """分析日志文件中装备品质的分布情况"""
    print("开始分析装备品质分布...")
    
    # 日志文件路径
    log_dir = "logs"
    log_files = glob.glob(os.path.join(log_dir, "*.log"))
    
    # 正则表达式模式
    quality_selection_pattern = re.compile(r"随机选择的品质: (.+)")
    weights_pattern = re.compile(r"实际使用的权重: \[(.+)\]")
    equipment_drops_pattern = re.compile(r"掉落装备: (.+), 品质: (.+)")
    
    # 统计计数器
    quality_counts = Counter()
    total_quality_selections = 0
    
    quality_from_drops = Counter()
    equipment_drops = []
    
    random_values = []
    
    print(f"找到 {len(log_files)} 个日志文件")
    
    for log_file in log_files:
        print(f"分析日志文件: {log_file}")
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.readlines()
            
            for line in log_content:
                # 捕获随机选择的品质
                quality_match = quality_selection_pattern.search(line)
                if quality_match:
                    quality = quality_match.group(1)
                    quality_counts[quality] += 1
                    total_quality_selections += 1
                
                # 捕获掉落的装备
                drop_match = equipment_drops_pattern.search(line)
                if drop_match:
                    item_name = drop_match.group(1)
                    quality = drop_match.group(2)
                    quality_from_drops[quality] += 1
                    equipment_drops.append((item_name, quality))
        
        except Exception as e:
            print(f"处理日志文件 {log_file} 时出错: {e}")
    
    # 输出统计结果
    print("\n品质生成统计:")
    if total_quality_selections > 0:
        for quality, count in quality_counts.items():
            percentage = (count / total_quality_selections) * 100
            print(f"  {quality}: {count} 次 ({percentage:.2f}%)")
    else:
        print("  未找到品质生成记录")
    
    print("\n掉落装备品质统计:")
    total_drops = sum(quality_from_drops.values())
    if total_drops > 0:
        for quality, count in quality_from_drops.items():
            percentage = (count / total_drops) * 100
            print(f"  {quality}: {count} 件 ({percentage:.2f}%)")
    else:
        print("  未找到装备掉落记录")
    
    # 评估品质生成是否合理
    if total_quality_selections > 0:
        expected_distribution = {
            "普通": 0.7273,  # 80/110
            "精良": 0.1364,  # 15/110
            "稀有": 0.0909,  # 10/110
            "史诗": 0.0364,  # 4/110
            "传说": 0.0091   # 1/110
        }
        
        print("\n品质生成系统评估:")
        total_diff = 0
        for quality, expected in expected_distribution.items():
            actual = quality_counts.get(quality, 0) / total_quality_selections
            diff = abs(actual - expected)
            total_diff += diff
            print(f"  {quality}: 期望 {expected:.4f}, 实际 {actual:.4f}, 差异 {diff:.4f}")
        
        avg_diff = total_diff / len(expected_distribution)
        print(f"\n平均差异: {avg_diff:.4f}")
        
        if avg_diff < 0.05:
            print("评估结果: 品质生成系统工作正常")
        elif avg_diff < 0.1:
            print("评估结果: 品质生成系统存在轻微偏差")
        else:
            print("评估结果: 品质生成系统可能存在问题")
    
    return quality_counts, quality_from_drops, equipment_drops

if __name__ == "__main__":
    analyze_quality_distribution() 