"""
技能管理系统 - 重新设计版本
"""
import time
import logging
from core.config import GameConfig

# 获取日志记录器
logger = logging.getLogger(__name__)

class SkillSlot:
    """技能槽位类 - 重新设计版本"""
    def __init__(self, slot_id, skill_id=None, enabled=True, auto_cast=False):
        """初始化技能槽位

        参数:
            slot_id: 槽位ID（0表示初始技能槽，1-6表示常规技能槽）
            skill_id: 技能ID
            enabled: 是否启用技能
            auto_cast: 是否自动释放（主动技能），默认为False
        """
        self.slot_id = slot_id  # 槽位ID
        self.skill_id = skill_id  # 技能ID
        self.enabled = enabled  # 是否启用技能
        self.auto_cast = auto_cast  # 是否自动释放（仅对主动技能有效）
        self.last_cast_time = 0  # 上次释放时间
        self.cooldown = 0  # 冷却时间

    def is_initial_slot(self):
        """是否是初始技能槽（0号槽位）"""
        return self.slot_id == 0

    def is_empty(self):
        """槽位是否为空"""
        return self.skill_id is None

    def set_skill(self, skill_id, cooldown=0):
        """设置技能

        参数:
            skill_id: 技能ID
            cooldown: 冷却时间
        """
        self.skill_id = skill_id
        self.cooldown = cooldown
        # 默认启用新添加的技能
        self.enabled = True
        # 默认自动释放
        self.auto_cast = True

    def toggle_enabled(self):
        """切换技能启用状态

        返回:
            bool: 切换后的启用状态
        """
        # 允许禁用初始技能槽
        self.enabled = not self.enabled
        return self.enabled

    def toggle_auto_cast(self):
        """切换技能自动释放状态

        返回:
            bool: 切换后的自动释放状态
        """
        self.auto_cast = not self.auto_cast
        return self.auto_cast

    def can_cast(self, current_time):
        """检查是否可以释放技能

        参数:
            current_time: 当前时间

        返回:
            bool: 是否可以释放技能
        """
        # 检查技能是否存在且已启用
        if not self.skill_id or not self.enabled:
            return False

        # 检查冷却时间
        return current_time - self.last_cast_time >= self.cooldown

    def update_cooldown(self, player):
        """更新技能冷却时间

        参数:
            player: 玩家对象
        """
        if not self.skill_id:
            return

        try:
            skill_config = GameConfig.get_skill(self.skill_id, player.character_class)
            if skill_config:
                cooldown = skill_config.get("cooldown", 0)
                if isinstance(cooldown, list):
                    skill_level = player.skills.get(self.skill_id, 1)
                    if skill_level <= len(cooldown):
                        cooldown = cooldown[skill_level - 1]
                    else:
                        cooldown = cooldown[-1]
                self.cooldown = cooldown
        except Exception as e:
            logger.error(f"更新技能冷却时间失败: {e}")


class SkillManager:
    """技能管理器类 - 重新设计版本"""
    def __init__(self, player):
        """初始化技能管理器

        参数:
            player: 玩家对象
        """
        self.player = player
        self.skill_slots = {}  # 所有技能槽位
        self.initialize()

    def initialize(self):
        """初始化技能槽位"""
        # 创建初始技能槽（0号槽位）
        self.skill_slots[0] = SkillSlot(0)

        # 创建常规技能槽（1-6）
        for i in range(1, 7):
            self.skill_slots[i] = SkillSlot(i)

        # 设置初始技能
        self.setup_initial_skill()

        # 加载玩家已有的技能槽配置
        self.load_player_skill_slots()

        # 自动分配所有已学习的主动技能到技能槽
        self.auto_assign_active_skills()

    def setup_initial_skill(self):
        """设置初始技能到0号槽位"""
        initial_skill_id = self.get_initial_skill_id()
        if not initial_skill_id:
            return

        # 确保玩家学习了初始技能
        if initial_skill_id not in self.player.skills or self.player.skills[initial_skill_id] <= 0:
            self.player.skills[initial_skill_id] = 1
            logger.info(f"自动学习初始技能: {initial_skill_id}")

        # 不再自动启用初始技能，允许玩家禁用初始技能
        # 检查初始技能是否被禁用（仅用于日志记录）
        if hasattr(self.player, 'disabled_skills') and initial_skill_id in self.player.disabled_skills:
            logger.info(f"初始技能 {initial_skill_id} 当前处于禁用状态")

        # 设置初始技能到0号槽位
        initial_slot = self.skill_slots[0]
        initial_slot.set_skill(initial_skill_id)
        initial_slot.auto_cast = True  # 初始技能默认自动释放
        initial_slot.update_cooldown(self.player)

        # 同步到玩家对象
        if hasattr(self.player, 'skill_slots'):
            self.player.skill_slots[0] = initial_skill_id
        else:
            self.player.skill_slots = {0: initial_skill_id}

        logger.info(f"设置初始技能 {initial_skill_id} 到0号槽位")

    def load_player_skill_slots(self):
        """加载玩家已有的技能槽配置"""
        if not hasattr(self.player, 'skill_slots'):
            return

        # 获取玩家的主动技能列表
        active_skills = self.get_active_skills()

        # 加载玩家的技能槽配置
        for slot_id_str, skill_id in self.player.skill_slots.items():
            try:
                # 将字符串键转换为整数（兼容旧存档）
                if slot_id_str == "initial":
                    slot_id = 0
                else:
                    slot_id = int(slot_id_str) if isinstance(slot_id_str, str) else slot_id_str

                # 检查技能是否有效
                if skill_id not in active_skills and slot_id != 0:  # 0号槽位是初始技能，可能不在active_skills中
                    continue

                # 设置技能到槽位
                if slot_id in self.skill_slots:
                    self.skill_slots[slot_id].set_skill(skill_id)
                    self.skill_slots[slot_id].update_cooldown(self.player)
                    logger.info(f"加载玩家技能槽配置: 槽位 {slot_id} -> 技能 {skill_id}")
            except (ValueError, TypeError) as e:
                logger.warning(f"加载技能槽位时出错: {e}, slot_id={slot_id_str}, skill_id={skill_id}")

        # 加载自动释放配置
        if hasattr(self.player, 'auto_cast_skills'):
            for slot_id_str in self.player.auto_cast_skills:
                try:
                    # 将字符串键转换为整数（兼容旧存档）
                    if slot_id_str == "initial":
                        slot_id = 0
                    else:
                        slot_id = int(slot_id_str) if isinstance(slot_id_str, str) else slot_id_str

                    if slot_id in self.skill_slots:
                        self.skill_slots[slot_id].auto_cast = True
                        logger.info(f"设置技能槽 {slot_id} 为自动释放")
                except (ValueError, TypeError) as e:
                    logger.warning(f"加载自动释放配置时出错: {e}, slot_id={slot_id_str}")

    def auto_assign_active_skills(self):
        """自动将所有已学习的主动技能分配到技能槽"""
        # 获取玩家的主动技能列表
        active_skills = self.get_active_skills()

        # 获取已分配的技能
        assigned_skills = set()
        for slot in self.skill_slots.values():
            if slot.skill_id:
                assigned_skills.add(slot.skill_id)

        # 找出未分配的技能
        unassigned_skills = [skill_id for skill_id in active_skills if skill_id not in assigned_skills]

        # 将未分配的技能分配到空闲槽位
        for skill_id in unassigned_skills:
            # 跳过初始技能，它应该只在0号槽位
            if self.is_initial_skill(skill_id):
                continue

            # 查找空闲槽位（从1开始，跳过0号初始技能槽）
            for slot_id in range(1, 7):
                if slot_id in self.skill_slots and self.skill_slots[slot_id].is_empty():
                    # 分配技能到槽位
                    self.skill_slots[slot_id].set_skill(skill_id)
                    self.skill_slots[slot_id].update_cooldown(self.player)

                    # 同步到玩家对象
                    if hasattr(self.player, 'skill_slots'):
                        self.player.skill_slots[slot_id] = skill_id
                    else:
                        self.player.skill_slots = {slot_id: skill_id}

                    logger.info(f"自动分配技能 {skill_id} 到槽位 {slot_id}")
                    break

    def get_initial_skill_id(self):
        """获取初始技能ID

        返回:
            str: 初始技能ID，如果没有则返回None
        """
        if self.player.character_class == "法师":
            return "fireball"  # 火球术
        elif self.player.character_class == "牧师":
            return "heal"  # 治愈术
        return None

    def is_initial_skill(self, skill_id):
        """检查是否是初始技能

        参数:
            skill_id: 技能ID

        返回:
            bool: 是否是初始技能
        """
        initial_skill_id = self.get_initial_skill_id()
        return initial_skill_id and skill_id == initial_skill_id

    def get_active_skills(self):
        """获取玩家的主动技能列表

        返回:
            list: 主动技能ID列表
        """
        active_skills = []
        for skill_id, level in self.player.skills.items():
            if level <= 0 or (hasattr(self.player, 'disabled_skills') and skill_id in self.player.disabled_skills):
                continue

            try:
                skill_config = GameConfig.get_skill(skill_id, self.player.character_class)
                if skill_config and skill_config["type"] == "active":
                    active_skills.append(skill_id)
            except Exception as e:
                logger.error(f"获取技能配置失败: {e}")

        return active_skills

    def get_slot(self, slot_id):
        """获取技能槽位

        参数:
            slot_id: 槽位ID

        返回:
            SkillSlot: 技能槽位对象，如果不存在则返回None
        """
        return self.skill_slots.get(slot_id)

    def set_skill(self, slot_id, skill_id):
        """设置技能到槽位

        参数:
            slot_id: 槽位ID
            skill_id: 技能ID

        返回:
            bool: 是否成功设置
        """
        # 检查槽位是否存在
        if slot_id not in self.skill_slots:
            logger.warning(f"槽位 {slot_id} 不存在")
            return False

        # 检查是否是初始技能槽
        if slot_id == 0:
            logger.warning("0号槽位（初始技能槽）不能手动设置技能")
            return False

        # 检查是否是初始技能
        if self.is_initial_skill(skill_id):
            logger.warning(f"初始技能 {skill_id} 只能放在0号槽位")
            return False

        # 检查技能是否已经在其他槽位中
        for other_slot_id, slot in self.skill_slots.items():
            if other_slot_id != slot_id and slot.skill_id == skill_id:
                # 如果技能已经在其他槽位中，不需要移除，只需记录日志
                logger.info(f"技能 {skill_id} 已存在于槽位 {other_slot_id}，但允许在多个槽位中使用")

        # 设置技能
        self.skill_slots[slot_id].set_skill(skill_id)
        self.skill_slots[slot_id].update_cooldown(self.player)

        # 同步到玩家对象
        if hasattr(self.player, 'skill_slots'):
            self.player.skill_slots[slot_id] = skill_id
        else:
            self.player.skill_slots = {slot_id: skill_id}

        logger.info(f"设置技能 {skill_id} 到槽位 {slot_id}")
        return True

    def toggle_skill_enabled(self, slot_id):
        """切换技能启用状态

        参数:
            slot_id: 槽位ID

        返回:
            bool: 切换后的启用状态，如果失败则返回None
        """
        # 检查槽位是否存在
        if slot_id not in self.skill_slots:
            logger.warning(f"槽位 {slot_id} 不存在")
            return None

        # 获取槽位
        slot = self.skill_slots[slot_id]

        # 检查槽位是否有技能
        if not slot.skill_id:
            logger.warning(f"槽位 {slot_id} 没有技能")
            return None

        # 切换启用状态
        enabled = slot.toggle_enabled()

        # 同步到玩家对象
        if not hasattr(self.player, 'skill_enabled_status'):
            self.player.skill_enabled_status = {}

        self.player.skill_enabled_status[slot_id] = enabled

        logger.info(f"切换技能槽 {slot_id} 的启用状态为: {enabled}")
        return enabled

    def toggle_auto_cast(self, slot_id):
        """切换技能自动释放状态

        参数:
            slot_id: 槽位ID

        返回:
            bool: 切换后的自动释放状态，如果失败则返回None
        """
        # 检查槽位是否存在
        if slot_id not in self.skill_slots:
            logger.warning(f"槽位 {slot_id} 不存在")
            return None

        # 获取槽位
        slot = self.skill_slots[slot_id]

        # 检查槽位是否有技能
        if not slot.skill_id:
            logger.warning(f"槽位 {slot_id} 没有技能")
            return None

        # 检查技能是否已启用
        if not slot.enabled:
            logger.warning(f"槽位 {slot_id} 的技能已禁用，无法切换自动释放状态")
            return None

        # 切换自动释放状态
        auto_cast = slot.toggle_auto_cast()

        # 同步到玩家对象
        if auto_cast:
            if not hasattr(self.player, 'auto_cast_skills'):
                self.player.auto_cast_skills = {}
            self.player.auto_cast_skills[slot_id] = True
        elif hasattr(self.player, 'auto_cast_skills') and slot_id in self.player.auto_cast_skills:
            del self.player.auto_cast_skills[slot_id]

        logger.info(f"切换技能槽 {slot_id} 的自动释放状态为: {auto_cast}")
        return auto_cast

    def clear_slot(self, slot_id):
        """清空技能槽位

        参数:
            slot_id: 槽位ID

        返回:
            bool: 是否成功清空
        """
        # 检查槽位是否存在
        if slot_id not in self.skill_slots:
            logger.warning(f"槽位 {slot_id} 不存在")
            return False

        # 检查是否是初始技能槽
        if slot_id == 0 or self.skill_slots[slot_id].is_initial_slot():
            logger.warning("初始技能槽不能清空")
            return False

        # 获取技能ID
        skill_id = self.skill_slots[slot_id].skill_id
        if not skill_id:
            logger.warning(f"槽位 {slot_id} 已经是空的")
            return False

        # 允许清空初始技能
        # 但记录日志以便追踪
        if self.is_initial_skill(skill_id):
            logger.info(f"清空初始技能 {skill_id} 从槽位 {slot_id}")

        # 清空槽位
        self.skill_slots[slot_id].skill_id = None
        self.skill_slots[slot_id].auto_cast = False
        self.skill_slots[slot_id].enabled = True  # 重置启用状态

        # 同步到玩家对象
        if hasattr(self.player, 'skill_slots') and slot_id in self.player.skill_slots:
            del self.player.skill_slots[slot_id]

        # 从自动释放集合中移除
        if hasattr(self.player, 'auto_cast_skills') and slot_id in self.player.auto_cast_skills:
            del self.player.auto_cast_skills[slot_id]

        # 从启用状态集合中移除
        if hasattr(self.player, 'skill_enabled_status') and slot_id in self.player.skill_enabled_status:
            del self.player.skill_enabled_status[slot_id]

        logger.info(f"清空技能槽位 {slot_id}")
        return True

    def get_available_skills(self):
        """获取所有可用的主动技能

        返回:
            list: 包含(skill_id, skill_config)元组的列表
        """
        # 获取玩家的主动技能列表
        active_skills = self.get_active_skills()

        # 获取初始技能ID
        initial_skill_id = self.get_initial_skill_id()

        # 构建可用技能列表
        available_skills = []
        for skill_id in active_skills:
            # 跳过初始技能，它只能放在0号槽位
            if initial_skill_id and skill_id == initial_skill_id:
                continue

            # 获取技能配置
            try:
                skill_config = GameConfig.get_skill(skill_id, self.player.character_class)
                if skill_config:
                    available_skills.append((skill_id, skill_config))
            except Exception as e:
                logger.error(f"获取技能配置失败: {e}")

        return available_skills

    def auto_cast_skills(self, game_manager):
        """自动释放技能

        参数:
            game_manager: 游戏管理器对象
        """
        current_time = time.time()

        # 检查是否在战斗中
        if not game_manager.in_battle:
            return

        # 检查玩家是否存在
        if not self.player:
            return

        # 检查玩家是否死亡
        if self.player.is_dead:
            return

        # 检查全局技能冷却
        if hasattr(self.player, 'get_global_cooldown_info'):
            global_cooldown_info = self.player.get_global_cooldown_info()
            global_cooldown_remaining = global_cooldown_info.get('remaining', 0)

            # 如果全局冷却中，不释放任何技能
            if global_cooldown_remaining > 0:
                return

        # 获取当前怪物
        current_monster = None
        if hasattr(game_manager, "battle_system") and game_manager.battle_system:
            current_monster = game_manager.battle_system.monster
        elif hasattr(game_manager, "current_enemy"):
            current_monster = game_manager.current_enemy

        # 检查当前怪物是否可以被魅惑
        monster_can_be_charmed = False
        if current_monster:
            # 定义可魅惑的怪物白名单
            CHARM_ALLOWED_MONSTERS = [
                "鸡", "鹿", "羊", "钉耙猫", "多钩猫", "虎卫",
                "半兽人", "森林雪人", "狼", "红蛇", "盔甲虫",
                "猎鹰", "蜘蛛", "多角虫", "虎蛇", "洞蛆",
                "半兽战士", "蜈蚣", "黑色恶蛆", "半兽勇士",
                "黑暗战士", "巨型多角虫", "钳虫", "契蛾",
                "牛头魔", "天狼蜘蛛"
            ]
            monster_name = getattr(current_monster, "name", "")
            if monster_name in CHARM_ALLOWED_MONSTERS:
                monster_can_be_charmed = True

        # 如果当前怪物可以被魅惑，优先使用诱惑之光技能
        if monster_can_be_charmed:
            # 查找诱惑之光技能
            charm_skill_id = "charm_light"
            charm_skill_slot = None

            # 遍历所有技能槽位，查找诱惑之光技能
            for slot_id, slot in self.skill_slots.items():
                if slot.skill_id == charm_skill_id and slot.enabled and slot.auto_cast:
                    charm_skill_slot = slot

                    # 检查技能是否在冷却中
                    if not slot.can_cast(current_time):
                        break

                    # 检查玩家技能冷却
                    if hasattr(self.player, 'skill_cooldowns') and slot.skill_id in self.player.skill_cooldowns:
                        if current_time < self.player.skill_cooldowns[slot.skill_id]:
                            break

                    # 尝试使用诱惑之光技能
                    logger.info(f"优先自动释放诱惑之光技能: {slot.skill_id}")
                    skill_result = game_manager.player.use_skill(slot.skill_id)

                    if skill_result:
                        # 更新最后释放时间
                        slot.last_cast_time = current_time
                        # 应用技能效果
                        self._apply_skill_effect(game_manager, skill_result)
                        # 成功使用诱惑之光技能后，直接返回，不再使用其他技能
                        return
                    break

        # 遍历所有技能槽位
        for slot_id, slot in self.skill_slots.items():
            # 检查技能是否已启用
            if not slot.enabled:
                continue

            # 检查是否自动释放
            if not slot.auto_cast:
                continue

            # 检查是否有技能
            if not slot.skill_id:
                continue

            # 检查技能是否在冷却中
            if not slot.can_cast(current_time):
                continue

            # 检查玩家技能冷却
            if hasattr(self.player, 'skill_cooldowns') and slot.skill_id in self.player.skill_cooldowns:
                if current_time < self.player.skill_cooldowns[slot.skill_id]:
                    continue

            # 尝试使用技能
            logger.info(f"自动释放技能槽 {slot_id} 中的技能: {slot.skill_id}")
            skill_result = game_manager.player.use_skill(slot.skill_id)

            if skill_result:
                # 更新最后释放时间
                slot.last_cast_time = current_time
                # 应用技能效果
                self._apply_skill_effect(game_manager, skill_result)

    def _apply_skill_effect(self, game_manager, skill_result):
        """应用技能效果

        参数:
            game_manager: 游戏管理器对象
            skill_result: 技能使用结果
        """
        try:
            if not skill_result:
                return

            effect_type = skill_result.get("effect_type")
            effect_value = skill_result.get("effect_value")
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))

            logger.info(f"自动释放技能效果应用: {skill_name}, 效果类型: {effect_type}")

            # 根据效果类型执行不同逻辑
            if effect_type == "damage_multiplier":
                # 烈火剑法：造成倍率伤害
                self._apply_damage_multiplier_skill(game_manager, skill_result)
            elif effect_type == "stun":
                # 野蛮冲撞：眩晕目标
                self._apply_stun_skill(game_manager, skill_result)
            elif effect_type == "aoe_damage":
                # 半月弯刀：对额外目标造成伤害
                self._apply_aoe_skill(game_manager, skill_result)
            elif effect_type == "heal":
                # 治疗术：恢复生命值
                self._apply_heal_skill(game_manager, skill_result)
            elif effect_type == "summon":
                # 召唤技能：召唤骷髅或神兽
                self._apply_summon_skill(game_manager, skill_result)
            elif effect_type == "direct_damage":
                # 直接伤害：造成直接攻击伤害
                self._apply_direct_damage_skill(game_manager, skill_result)
            elif effect_type == "damage_percent":
                # 伤害百分比增加：造成百分比额外伤害
                self._apply_damage_percent_skill(game_manager, skill_result)
            elif effect_type == "extra_damage":
                # 额外伤害：在基础伤害上增加额外伤害
                self._apply_extra_damage_skill(game_manager, skill_result)
            elif effect_type == "poison":
                # 毒药：造成持续伤害
                self._apply_poison_skill(game_manager, skill_result)
            elif effect_type == "ground_damage":
                # 地面伤害：在地面创建伤害区域（火墙等）
                self._apply_ground_damage_skill(game_manager, skill_result)
            elif effect_type == "charm":
                # 诱惑/控制技能：控制或驯服怪物
                self._apply_charm_skill(game_manager, skill_result)
            elif effect_type == "defense":
                # 物理防御增益技能
                self._apply_defense_buff_skill(game_manager, skill_result)
            elif effect_type == "magic_defense":
                # 魔法防御增益技能
                self._apply_magic_defense_buff_skill(game_manager, skill_result)
            elif effect_type == "agility":
                # 敏捷增益技能
                self._apply_agility_buff_skill(game_manager, skill_result)
            elif effect_type == "magic_damage":
                # 魔法伤害技能
                self._apply_magic_damage_skill(game_manager, skill_result)
            elif effect_type == "percentage_health_damage":
                # 基于生命值百分比的伤害技能
                self._apply_percentage_health_damage_skill(game_manager, skill_result)
            elif effect_type == "damage_reduction":
                # 伤害减免技能（如魔法盾）
                self._apply_damage_reduction_skill(game_manager, skill_result)
            else:
                logger.warning(f"未知的技能效果类型: {effect_type}")

        except Exception as e:
            logger.error(f"应用技能效果时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _apply_damage_multiplier_skill(self, game_manager, skill_result):
        """应用倍率伤害技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 1.0)

        # 获取目标怪物
        monster = game_manager.current_enemy
        if not monster:
            logger.warning(f"使用{skill_name}失败: 没有目标")
            return

        # 计算玩家基础伤害
        player = game_manager.player
        base_damage, is_crit = player.calculate_damage()

        # 应用技能倍率
        skill_damage = int(base_damage * effect_value)

        # 应用伤害
        monster.hp = max(0, monster.hp - skill_damage)

        # 添加战斗日志
        crit_text = "暴击!" if is_crit else ""
        game_manager.add_log(f"[{skill_name}]{crit_text} 对 {monster.name} 造成 {skill_damage} 点伤害!")

        # 检查怪物是否死亡
        if monster.hp <= 0:
            game_manager.handle_monster_death()

    def _apply_heal_skill(self, game_manager, skill_result):
        """应用治疗技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 0.2)

        # 获取玩家
        player = game_manager.player
        if not player:
            logger.warning(f"使用{skill_name}失败: 找不到玩家")
            return

        # 计算治疗量
        heal_amount = 0
        if isinstance(effect_value, (int, float)):
            if effect_value < 1:  # 假设小于1的值为百分比
                heal_amount = int(player.max_hp * effect_value)
            else:
                heal_amount = int(effect_value)

        # 应用治疗效果
        old_hp = player.hp
        player.hp = min(player.max_hp, player.hp + heal_amount)
        actual_heal = player.hp - old_hp

        # 添加战斗日志
        game_manager.add_log(f"[{skill_name}]恢复了 {actual_heal} 点生命值!")

    def _apply_charm_skill(self, game_manager, skill_result):
        """应用诱惑/控制技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 0.5)  # 成功率 0-1
        level_diff = skill_result.get("level_diff", 2)  # 默认可控制比自己高2级的怪物

        # 获取目标怪物
        monster = None
        # 尝试从战斗系统获取怪物
        if hasattr(game_manager, "battle_system") and game_manager.battle_system:
            monster = game_manager.battle_system.monster
        # 如果战斗系统中没有怪物，尝试从current_enemy属性获取
        if not monster and hasattr(game_manager, "current_enemy"):
            monster = game_manager.current_enemy

        if not monster:
            logger.warning(f"使用{skill_name}失败: 没有目标")
            return

        # 检查怪物是否可以被魅惑
        player = game_manager.player
        if monster.level > player.level + level_diff:
            game_manager.add_log(f"[{skill_name}]失败: {monster.name} 等级过高，无法魅惑!")
            return

        # 计算成功率
        import random
        if random.random() < effect_value:
            # 魅惑成功
            game_manager.add_log(f"[{skill_name}]成功魅惑了 {monster.name}!")

            # 将怪物添加到召唤物列表
            if hasattr(game_manager, 'summoned_creatures'):
                game_manager.summoned_creatures.append(monster)
            else:
                game_manager.summoned_creatures = [monster]

            # 结束当前战斗
            game_manager.end_battle()
        else:
            # 魅惑失败
            game_manager.add_log(f"[{skill_name}]魅惑 {monster.name} 失败!")

    def _apply_direct_damage_skill(self, game_manager, skill_result):
        """应用直接伤害技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 100)

        # 获取目标怪物
        monster = game_manager.current_enemy
        if not monster:
            logger.warning(f"使用{skill_name}失败: 没有目标")
            return

        # 应用直接伤害
        monster.hp = max(0, monster.hp - effect_value)

        # 添加战斗日志
        game_manager.add_log(f"[{skill_name}] 对 {monster.name} 造成 {effect_value} 点伤害!")

        # 检查怪物是否死亡
        if monster.hp <= 0:
            game_manager.handle_monster_death()

    # 其他技能效果的占位符方法
    def _apply_stun_skill(self, game_manager, skill_result):
        """应用眩晕技能"""
        logger.info("眩晕技能效果应用（占位符）")

    def _apply_aoe_skill(self, game_manager, skill_result):
        """应用AOE技能"""
        logger.info("AOE技能效果应用（占位符）")

    def _apply_summon_skill(self, game_manager, skill_result):
        """应用召唤技能"""
        logger.info("召唤技能效果应用（占位符）")

    def _apply_damage_percent_skill(self, game_manager, skill_result):
        """应用伤害百分比技能"""
        logger.info("伤害百分比技能效果应用（占位符）")

    def _apply_extra_damage_skill(self, game_manager, skill_result):
        """应用额外伤害技能"""
        logger.info("额外伤害技能效果应用（占位符）")

    def _apply_poison_skill(self, game_manager, skill_result):
        """应用毒药技能"""
        logger.info("毒药技能效果应用（占位符）")

    def _apply_ground_damage_skill(self, game_manager, skill_result):
        """应用地面伤害技能"""
        logger.info("地面伤害技能效果应用（占位符）")

    def _apply_defense_buff_skill(self, game_manager, skill_result):
        """应用物理防御增益技能"""
        logger.info("物理防御增益技能效果应用（占位符）")

    def _apply_magic_defense_buff_skill(self, game_manager, skill_result):
        """应用魔法防御增益技能"""
        logger.info("魔法防御增益技能效果应用（占位符）")

    def _apply_agility_buff_skill(self, game_manager, skill_result):
        """应用敏捷增益技能"""
        logger.info("敏捷增益技能效果应用（占位符）")

    def _apply_magic_damage_skill(self, game_manager, skill_result):
        """应用魔法伤害技能"""
        logger.info("魔法伤害技能效果应用（占位符）")

    def _apply_percentage_health_damage_skill(self, game_manager, skill_result):
        """应用基于生命值百分比的伤害技能"""
        logger.info("基于生命值百分比的伤害技能效果应用（占位符）")

    def _apply_damage_reduction_skill(self, game_manager, skill_result):
        """应用伤害减免技能"""
        logger.info("伤害减免技能效果应用（占位符）")