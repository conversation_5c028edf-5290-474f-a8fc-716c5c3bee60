import pygame
from typing import Dict, List, Callable, Any, Optional, Tuple

from ui.ui_manager import Screen
from ui.components import Button, Panel, Text, ImageButton, InputBox
from utils.logger import logger
from utils.resource_manager import resources
from core.game import Game
from core.class_stats import CLASS_STATS, get_class_stats

class CharacterCreation(Screen):
    """角色创建界面"""
    
    def __init__(self, ui_manager, game_manager: Game):
        """
        初始化角色创建界面
        
        参数:
            ui_manager: UI管理器实例
            game_manager: 游戏管理器实例
        """
        super().__init__("character_creation")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.selected_class = "战士"  # 默认选择战士
        self.selected_gender = "男"   # 默认选择男性
        self.player_name = ""  # 添加玩家名字变量
        
        # 职业按钮引用
        self.warrior_btn = None
        self.mage_btn = None
        self.taoist_btn = None
        
        # 性别按钮引用
        self.male_btn = None
        self.female_btn = None
        
        # 名字输入框引用
        self.name_input = None  # 添加名字输入框引用
        
        # 描述文本
        self.description_text = None
        self.stats_text = None
        
        self._create_components()
    
    def _create_components(self):
        """创建界面组件"""
        # 获取屏幕尺寸
        screen_size = pygame.display.get_surface().get_size()
        
        # 创建背景
        self.background = Panel(
            pygame.Rect(0, 0, screen_size[0], screen_size[1]),
            color=(40, 40, 60),
            border_width=0
        )
        self.add_component(self.background)
        
        # 创建标题
        title_rect = pygame.Rect(
            (screen_size[0] - 400) // 2,
            50,
            400,
            60
        )
        title = self.ui_manager.create_text(
            title_rect,
            "创建角色",
            "chinese_title",
            (255, 255, 220),
            "center"
        )
        self.add_component(title)
        
        # 创建主面板
        panel_width = 600
        panel_height = 600  # 增加面板高度以容纳性别选择
        panel_rect = pygame.Rect(
            (screen_size[0] - panel_width) // 2,
            (screen_size[1] - panel_height) // 2,
            panel_width,
            panel_height
        )
        main_panel = self.ui_manager.create_panel(
            panel_rect,
            color=(30, 30, 50),  # 深色背景
            border_color=(80, 80, 120),  # 边框颜色
            border_width=2,
            alpha=180  # 半透明
        )
        self.add_component(main_panel)
        
        # 职业选择标题
        class_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 30,
            200,
            30
        )
        class_title = self.ui_manager.create_text(
            class_title_rect,
            "选择职业:",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(class_title)
        
        # 职业选择按钮 - 战士
        warrior_btn_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 80,
            150,
            40
        )
        self.warrior_btn = self.ui_manager.create_button(
            warrior_btn_rect,
            "战士",
            lambda: self._on_class_select("战士"),
            "chinese_large"
        )
        self.add_component(self.warrior_btn)
        
        # 职业选择按钮 - 法师
        mage_btn_rect = pygame.Rect(
            panel_rect.x + 220,
            panel_rect.y + 80,
            150,
            40
        )
        self.mage_btn = self.ui_manager.create_button(
            mage_btn_rect,
            "法师",
            lambda: self._on_class_select("法师"),
            "chinese_large"
        )
        self.add_component(self.mage_btn)
        
        # 职业选择按钮 - 道士
        taoist_btn_rect = pygame.Rect(
            panel_rect.x + 390,
            panel_rect.y + 80,
            150,
            40
        )
        self.taoist_btn = self.ui_manager.create_button(
            taoist_btn_rect,
            "道士",
            lambda: self._on_class_select("道士"),
            "chinese_large"
        )
        self.add_component(self.taoist_btn)
        
        # 性别选择标题
        gender_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 140,
            200,
            30
        )
        gender_title = self.ui_manager.create_text(
            gender_title_rect,
            "选择性别:",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(gender_title)
        
        # 性别选择按钮 - 男
        male_btn_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 180,
            150,
            40
        )
        self.male_btn = self.ui_manager.create_button(
            male_btn_rect,
            "男",
            lambda: self._on_gender_select("男"),
            "chinese_large"
        )
        self.add_component(self.male_btn)
        
        # 性别选择按钮 - 女
        female_btn_rect = pygame.Rect(
            panel_rect.x + 220,
            panel_rect.y + 180,
            150,
            40
        )
        self.female_btn = self.ui_manager.create_button(
            female_btn_rect,
            "女",
            lambda: self._on_gender_select("女"),
            "chinese_large"
        )
        self.add_component(self.female_btn)
        
        # 职业介绍标题
        description_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 240,
            200,
            30
        )
        description_title = self.ui_manager.create_text(
            description_title_rect,
            "职业介绍:",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(description_title)
        
        # 职业介绍面板
        description_panel_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 280,
            panel_width - 100,
            120
        )
        self.description_panel = Panel(
            description_panel_rect,
            color=(30, 30, 50),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(self.description_panel)
        
        # 职业介绍文本
        self.description_text = self.ui_manager.create_text(
            pygame.Rect(
                description_panel_rect.x + 10,
                description_panel_rect.y + 10,
                description_panel_rect.width - 20,
                description_panel_rect.height - 20
            ),
            self._get_class_description("战士"),
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.description_text)
        
        # 属性面板
        stats_panel_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 410,
            panel_width - 100,
            140  # 增加高度以容纳角色名称输入框
        )
        self.stats_panel = Panel(
            stats_panel_rect,
            color=(30, 30, 50),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(self.stats_panel)
        
        # 属性文本
        self.stats_text = self.ui_manager.create_text(
            pygame.Rect(
                stats_panel_rect.x + 10,
                stats_panel_rect.y + 10,
                stats_panel_rect.width - 20,
                60  # 给属性文本留出足够空间
            ),
            self._get_class_stats("战士"),
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.stats_text)
        
        # 玩家名称标题 - 放在属性面板中
        name_title_rect = pygame.Rect(
            stats_panel_rect.x + 10,
            stats_panel_rect.y + 80,  # 位置在属性文本下方
            100,
            30
        )
        name_title = self.ui_manager.create_text(
            name_title_rect,
            "角色名称:",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(name_title)
        
        # 玩家名称输入框 - 放在属性面板中
        name_input_rect = pygame.Rect(
            stats_panel_rect.x + 120,
            stats_panel_rect.y + 80,
            250,
            30
        )
        self.name_input = self.ui_manager.create_input_box(
            name_input_rect,
            "",
            self._on_name_change,
            max_length=10,  # 修改最大长度为10
            font_name="chinese_normal"
        )
        self.add_component(self.name_input)
        
        # 名称长度限制提示 - 放在属性面板中
        name_hint_rect = pygame.Rect(
            stats_panel_rect.x + 120,
            stats_panel_rect.y + 113,
            250,
            20
        )
        name_hint = self.ui_manager.create_text(
            name_hint_rect,
            "最多5个中文字或10个英文字",
            "chinese_small",
            (180, 180, 180),
            "left"
        )
        self.add_component(name_hint)
        
        # 创建角色按钮
        create_btn_rect = pygame.Rect(
            panel_rect.x + 150,
            panel_rect.y + panel_height - 60,
            120,
            40
        )
        create_btn = self.ui_manager.create_button(
            create_btn_rect,
            "创建",
            self._on_create_click,
            "chinese_large"
        )
        self.add_component(create_btn)
        
        # 返回按钮
        back_btn_rect = pygame.Rect(
            panel_rect.x + panel_width - 270,
            panel_rect.y + panel_height - 60,
            120,
            40
        )
        back_btn = self.ui_manager.create_button(
            back_btn_rect,
            "返回",
            self._on_back_click,
            "chinese_large"
        )
        self.add_component(back_btn)
        
        # 设置默认选中的职业
        self._update_class_selection("战士")
        
        # 设置默认选中的性别
        self._update_gender_selection("男")
    
    def _get_class_description(self, character_class: str) -> str:
        """获取职业描述"""
        descriptions = {
            "战士": "物理攻击职业，较高的生命值和防御力",
            "法师": "魔法攻击职业，较高的魔法攻击力，生命值和防御力较低",
            "道士": "辅助类职业，平衡的属性，可以召唤宠物协助战斗"
        }
        return descriptions.get(character_class, "未知职业")
    
    def _get_class_stats(self, character_class: str) -> str:
        """获取职业属性"""
        if character_class not in CLASS_STATS:
            return "未知职业"
        
        # 使用class_stats中的配置
        class_data = CLASS_STATS[character_class]
        
        # 获取更详细的属性信息
        class_stats = get_class_stats(character_class)
        base_stats = class_stats["base_stats"]
        
        # 生成属性描述文本
    
    def _on_class_select(self, character_class: str):
        """处理职业选择"""
        if character_class != self.selected_class:
            self.selected_class = character_class
            self._update_class_selection(character_class)
    
    def _update_class_selection(self, character_class: str):
        """更新职业选择的UI状态"""
        # 更新按钮颜色
        button_map = {
            "战士": self.warrior_btn,
            "法师": self.mage_btn,
            "道士": self.taoist_btn
        }
        
        for cls, btn in button_map.items():
            if cls == character_class:
                # 选中状态
                btn.colors["normal"] = (100, 100, 180)
            else:
                # 未选中状态
                btn.colors["normal"] = (60, 60, 100)
        
        # 更新职业描述和属性
        self.description_text.set_text(self._get_class_description(character_class))
        self.stats_text.set_text(self._get_class_stats(character_class))
    
    def calculate_name_length(self, text: str) -> int:
        """
        计算名称的有效长度
        中文字符算2个单位，英文和其他字符算1个单位
        
        参数:
            text: 要计算的文本
            
        返回:
            int: 计算后的有效长度
        """
        length = 0
        for char in text:
            # 判断是否是中文字符（Unicode范围）
            if '\u4e00' <= char <= '\u9fff':
                length += 2  # 中文字符算2个单位
            else:
                length += 1  # 其他字符算1个单位
        return length
    
    def _on_name_change(self, text):
        """处理名字输入变化"""
        # 限制名称长度 - 最多5个中文字符或10个英文字符
        effective_length = self.calculate_name_length(text)
        
        if effective_length > 10:  # 最大有效长度为10
            # 如果超出长度，需要截断
            # 从头开始计算有效长度，直到找到合适的截断点
            new_text = ""
            new_length = 0
            
            for char in text:
                char_length = 2 if '\u4e00' <= char <= '\u9fff' else 1
                if new_length + char_length <= 10:
                    new_text += char
                    new_length += char_length
                else:
                    break
            
            # 如果文本发生了变化，则更新输入框
            if new_text != text:
                self.name_input.set_text(new_text)
                text = new_text
                logger.debug(f"名称已截断至: {new_text} (有效长度: {new_length})")
        
        self.player_name = text
        logger.debug(f"玩家输入名称: {text} (有效长度: {effective_length})")
    
    def _on_create_click(self):
        """处理创建角色按钮点击"""
        # 检查名称是否为空
        if not self.player_name.strip():
            self.ui_manager.show_message(
                "提示",
                "请输入角色名称",
                None
            )
            return
        
        # 检查名称长度
        effective_length = self.calculate_name_length(self.player_name)
        if effective_length > 10:
            self.ui_manager.show_message(
                "提示",
                "角色名称过长，最多支持5个中文字或10个英文字",
                None
            )
            return
        elif effective_length < 2:
            self.ui_manager.show_message(
                "提示",
                "角色名称过短，请至少输入1个中文字或2个英文字",
                None
            )
            return
        
        # 创建新角色
        success = self.game_manager.create_new_character(self.selected_class, self.player_name, self.selected_gender)
        
        if success:
            logger.info(f"成功创建名为 {self.player_name} 的{self.selected_class}角色，性别: {self.selected_gender}")
            # 显示游戏主界面
            self.ui_manager.show_screen("game")
        else:
            logger.error(f"创建{self.selected_class}角色失败")
            # 显示错误消息
            self.ui_manager.show_message(
                "创建失败",
                "创建角色失败，请重试。",
                None
            )
    
    def _on_back_click(self):
        """处理返回按钮点击"""
        self.ui_manager.show_screen("main_menu")
    
    def _on_gender_select(self, gender: str):
        """处理性别选择"""
        if gender != self.selected_gender:
            self.selected_gender = gender
            self._update_gender_selection(gender)
    
    def _update_gender_selection(self, gender: str):
        """更新性别选择的UI状态"""
        # 更新按钮颜色
        button_map = {
            "男": self.male_btn,
            "女": self.female_btn
        }
        
        for gen, btn in button_map.items():
            if gen == gender:
                # 选中状态
                btn.colors["normal"] = (100, 100, 180)
            else:
                # 未选中状态
                btn.colors["normal"] = (60, 60, 100) 