import random
import logging
import sys

# 设置日志格式
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s',
                   stream=sys.stdout)
logger = logging.getLogger("blessing_oil_test")

def simulate_blessing_oil(luck_level, trials=10000):
    """
    模拟祝福油使用过程
    
    Args:
        luck_level: 当前幸运值
        trials: 测试次数
    
    Returns:
        dict: 包含各结果的次数和百分比
    """
    if luck_level >= 7:
        return {"error": "已达到最大幸运值"}
        
    # 根据当前幸运值确定成功率和降级率
    success_rates = [100, 50, 30, 20, 10, 10, 10]
    downgrade_rates = [0, 20, 30, 40, 40, 40, 40]
    
    success_rate = success_rates[luck_level]
    downgrade_rate = downgrade_rates[luck_level]
    
    # 计数器
    results = {
        "success": 0,  # 提升
        "downgrade": 0,  # 降级
        "unchanged": 0   # 不变
    }
    
    # 执行模拟
    for _ in range(trials):
        roll = random.randint(1, 100)
        
        if roll <= success_rate:
            results["success"] += 1
        elif roll <= success_rate + downgrade_rate:
            results["downgrade"] += 1
        else:
            results["unchanged"] += 1
    
    # 计算百分比
    results["success_pct"] = results["success"] / trials * 100
    results["downgrade_pct"] = results["downgrade"] / trials * 100
    results["unchanged_pct"] = results["unchanged"] / trials * 100
    
    return results

def run_test():
    """运行全面测试，测试所有幸运值级别"""
    logger.info("开始祝福油概率测试...")
    
    # 表头
    print("\n| 幸运值 | 期望提升% | 实际提升% | 期望降级% | 实际降级% | 期望不变% | 实际不变% |")
    print("|--------|----------|----------|----------|----------|----------|----------|")
    
    success_rates = [100, 50, 30, 20, 10, 10, 10]
    downgrade_rates = [0, 20, 30, 40, 40, 40, 40]
    
    # 测试各个级别
    for luck in range(7):
        # 理论概率
        success_rate = success_rates[luck]
        downgrade_rate = downgrade_rates[luck]
        unchanged_rate = 100 - success_rate - downgrade_rate
        
        # 模拟测试
        trials = 100000  # 大样本量以获得更准确的结果
        result = simulate_blessing_oil(luck, trials)
        
        # 输出结果
        print(f"| {luck} → {luck+1} | {success_rate:8.1f}% | {result['success_pct']:8.1f}% | "
              f"{downgrade_rate:8.1f}% | {result['downgrade_pct']:8.1f}% | "
              f"{unchanged_rate:8.1f}% | {result['unchanged_pct']:8.1f}% |")
    
    logger.info("祝福油概率测试完成")

if __name__ == "__main__":
    run_test()