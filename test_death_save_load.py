#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试玩家死亡状态的保存和加载
验证死亡时退出游戏或加载存档的行为
"""

import sys
import os
import time
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_death_state_save_load():
    """测试死亡状态的保存和加载"""
    print("=" * 60)
    print("测试玩家死亡状态的保存和加载")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.player import Player
        from core.game import Game
        from core.config import GameConfig
        
        # 初始化游戏配置
        GameConfig.initialize()
        
        print("1. 创建测试玩家")
        player = Player("战士", "测试战士", "男")
        print(f"   玩家创建成功: {player.name}, 死亡状态: {player.is_dead}")
        
        print("\n2. 模拟玩家死亡")
        player.is_dead = True
        player.last_death_time = time.time()
        player.hp = 0
        print(f"   设置死亡状态: is_dead={player.is_dead}, hp={player.hp}")
        
        print("\n3. 保存玩家数据")
        saved_data = player.to_dict()
        print(f"   保存的数据中是否包含死亡状态: {'is_dead' in saved_data}")
        if 'is_dead' in saved_data:
            print(f"   保存的死亡状态: {saved_data['is_dead']}")
        else:
            print("   ❌ 保存的数据中没有死亡状态字段！")
        
        print(f"   保存的HP: {saved_data.get('hp', '未找到')}")
        print(f"   保存的last_death_time: {'last_death_time' in saved_data}")
        
        print("\n4. 创建新玩家并加载数据")
        new_player = Player("战士", "新玩家", "男")
        print(f"   新玩家初始死亡状态: {new_player.is_dead}")
        
        print("\n5. 加载保存的数据")
        new_player.load_from_dict(saved_data)
        print(f"   加载后的死亡状态: {new_player.is_dead}")
        print(f"   加载后的HP: {new_player.hp}")
        
        # 检查结果
        if new_player.is_dead == player.is_dead:
            print("   ✅ 死亡状态正确保存和加载")
            return True
        else:
            print("   ❌ 死亡状态在加载时被重置了！")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_game_save_load_with_death():
    """测试游戏保存和加载时的死亡状态"""
    print("\n" + "=" * 60)
    print("测试游戏保存和加载时的死亡状态")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.player import Player
        from core.game import Game
        from core.config import GameConfig
        
        # 初始化游戏配置
        GameConfig.initialize()
        
        print("1. 创建游戏实例和玩家")
        game = Game()
        game.player = Player("法师", "测试法师", "女")
        game.player.game = game
        
        print(f"   玩家创建成功: {game.player.name}")
        print(f"   初始状态: is_dead={game.player.is_dead}, hp={game.player.hp}")
        
        print("\n2. 模拟玩家死亡")
        game.player.is_dead = True
        game.player.last_death_time = time.time()
        game.player.hp = 0
        print(f"   死亡后状态: is_dead={game.player.is_dead}, hp={game.player.hp}")
        
        print("\n3. 保存游戏数据")
        game_data = game.save_data()
        if game_data:
            player_data = game_data.get("player", {})
            print(f"   游戏数据保存成功")
            print(f"   玩家数据中的死亡状态: {'is_dead' in player_data}")
            if 'is_dead' in player_data:
                print(f"   保存的死亡状态: {player_data['is_dead']}")
            print(f"   保存的HP: {player_data.get('hp', '未找到')}")
        else:
            print("   ❌ 游戏数据保存失败")
            return False
        
        print("\n4. 创建新游戏实例并加载数据")
        new_game = Game()
        
        # 模拟从存档加载
        print("   模拟加载存档...")
        player_data = game_data.get("player", {})
        
        # 创建新玩家并加载数据
        from core.player import Player
        new_game.player = Player(
            character_class=player_data.get("character_class", "战士"),
            name=player_data.get("name", "未知"),
            gender=player_data.get("gender", "男")
        )
        new_game.player.game = new_game
        new_game.player.load_from_dict(player_data)
        
        print(f"   加载后玩家状态: is_dead={new_game.player.is_dead}, hp={new_game.player.hp}")
        
        # 检查结果
        if new_game.player.is_dead == game.player.is_dead:
            print("   ✅ 游戏保存和加载时死亡状态正确保持")
            return True
        else:
            print("   ❌ 游戏加载时死亡状态被重置了！")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_new_character_after_death():
    """测试死亡后创建新角色的行为"""
    print("\n" + "=" * 60)
    print("测试死亡后创建新角色的行为")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.player import Player
        from core.game import Game
        from core.config import GameConfig
        
        # 初始化游戏配置
        GameConfig.initialize()
        
        print("1. 创建游戏实例和第一个角色")
        game = Game()
        game.player = Player("道士", "第一个角色", "男")
        game.player.game = game
        
        print(f"   第一个角色: {game.player.name}")
        print(f"   初始状态: is_dead={game.player.is_dead}, hp={game.player.hp}")
        
        print("\n2. 模拟第一个角色死亡")
        game.player.is_dead = True
        game.player.last_death_time = time.time()
        game.player.hp = 0
        print(f"   死亡后状态: is_dead={game.player.is_dead}, hp={game.player.hp}")
        
        print("\n3. 创建新角色（模拟重新创建角色）")
        success = game.create_new_character("法师", "新角色", "女")
        
        if success:
            print(f"   新角色创建成功: {game.player.name}")
            print(f"   新角色状态: is_dead={game.player.is_dead}, hp={game.player.hp}")
            
            # 检查新角色是否是活着的
            if not game.player.is_dead and game.player.hp > 0:
                print("   ✅ 新角色创建后状态正常（活着）")
                return True
            else:
                print("   ❌ 新角色创建后状态异常")
                return False
        else:
            print("   ❌ 新角色创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("玩家死亡状态保存和加载测试")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    # 测试1：死亡状态的保存和加载
    result1 = test_death_state_save_load()
    test_results.append(("死亡状态保存和加载", result1))
    
    # 测试2：游戏保存和加载时的死亡状态
    result2 = test_game_save_load_with_death()
    test_results.append(("游戏保存加载死亡状态", result2))
    
    # 测试3：死亡后创建新角色
    result3 = test_create_new_character_after_death()
    test_results.append(("死亡后创建新角色", result3))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！死亡状态处理正常")
    else:
        print("⚠️  部分测试失败，需要修复死亡状态的保存和加载")
        print("\n分析结果：")
        print("- 如果死亡状态保存和加载失败：需要在to_dict和load_from_dict中添加死亡状态字段")
        print("- 如果游戏保存加载失败：需要确保游戏保存时包含完整的死亡状态")
        print("- 如果创建新角色失败：需要确保新角色创建时正确初始化状态")
    print("=" * 60)

if __name__ == "__main__":
    main()
